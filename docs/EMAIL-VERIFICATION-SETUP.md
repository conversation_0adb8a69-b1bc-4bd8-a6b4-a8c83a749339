# 📧 Email Verification System Setup

Complete setup guide for the newsletter email verification system using Convex actions and Mailtrap.

## 🎯 System Overview

This implementation provides a secure, double opt-in newsletter system with email verification:

1. **User subscribes** → Email saved as unverified
2. **Verification email sent** → Via Mailtrap with unique token
3. **User clicks link** → Redirected to verification page
4. **Email verified** → Status updated in database
5. **Welcome email sent** → Confirmation with unsubscribe link
6. **Unsubscribe available** → One-click unsubscribe with soft delete

## 🏗️ Architecture

```
Newsletter Form → Convex Action → Mailtrap → Verification Email
                       ↓
User Clicks Link → Verification Page → Convex Mutation → Database Update
                       ↓
                Welcome Email → Mailtrap → Welcome + Unsubscribe Link
                       ↓
User Clicks Unsubscribe → Unsubscribe Page → Convex Mutation → Soft Delete
```

## 🚀 Setup Instructions

### 1. Environment Variables

Create your `.env` file with these variables:

```env
# Convex Configuration
CONVEX_URL=your_convex_deployment_url
PUBLIC_CONVEX_URL=your_convex_deployment_url

# Mailtrap Configuration
MAILTRAP_TOKEN=your_mailtrap_api_token
MAILTRAP_SENDER_EMAIL=<EMAIL>

# Site Configuration
SITE_URL=https://saudecomalex.com
```

### 2. Initialize Convex

```bash
# Install Convex CLI if not already installed
npm install -g convex

# Initialize and start development
npx convex dev
```

This will:
- Generate API types
- Start the development server
- Provide your CONVEX_URL

### 3. Deploy Schema and Functions

```bash
# Deploy the schema and functions
npx convex deploy
```

### 4. Configure Mailtrap

1. Create account at [Mailtrap.io](https://mailtrap.io)
2. Go to Email API → API Tokens
3. Create a new token with sending permissions
4. Add token to your environment variables

### 5. Update Environment Variables

After running `npx convex dev`, update your `.env` file with the actual Convex URL:

```env
CONVEX_URL=https://your-actual-deployment.convex.cloud
PUBLIC_CONVEX_URL=https://your-actual-deployment.convex.cloud
```

## 📊 Database Schema

```typescript
newsletterSubscribers: {
  email: string,                    // Subscriber email (indexed)
  createdAt: number,               // Subscription timestamp
  isActive: boolean,               // Active status
  isVerified: boolean,             // Email verification status (indexed)
  verificationToken?: string,      // Unique verification token (indexed)
  verificationTokenExpiresAt?: number, // Token expiration (24 hours)
  verifiedAt?: number,             // Verification timestamp
  unsubscribeToken?: string,       // Unique unsubscribe token (indexed)
  unsubscribedAt?: number         // Unsubscribe timestamp
}
```

## 🎨 Features Implemented

### 📝 Newsletter Component (`src/components/Newsletter.astro`)
- **Direct Convex integration** - No API endpoints needed
- **Smart messaging** based on verification status
- **Loading animations** during form submission
- **Real-time validation** with helpful feedback
- **Responsive design** for all devices
- **Accessibility features** with proper ARIA labels

### 📄 Verification Page (`/verify`)
- **Static page** with query parameter handling (`/verify?token=xyz`)
- **Client-side verification** using Convex browser client
- **Beautiful success state** with celebration
- **Clear error handling** for expired/invalid tokens
- **Step-by-step instructions** for troubleshooting
- **Responsive design** with proper CTAs

### ⚡ Convex Functions

#### Mutations
- `subscribe`: Create/update unverified subscriber
- `verifyEmail`: Verify email with token
- `storeUnsubscribeToken`: Store unsubscribe token for subscriber
- `unsubscribeWithToken`: Unsubscribe using token (soft delete)
- `unsubscribe`: Unsubscribe by email (legacy method)

#### Queries
- `getSubscriberByToken`: Get subscriber info for verification
- `getSubscriberByUnsubscribeToken`: Get subscriber info for unsubscribe
- `getVerifiedSubscribers`: Admin query for active subscribers

#### Actions
- `sendVerificationEmail`: Send email via Mailtrap
- `sendWelcomeEmail`: Send welcome email after verification
- `subscribeWithVerification`: Combined subscribe + email send

## 📧 Email Template Features

### Professional Design
- **Responsive HTML** that works in all email clients
- **Branded styling** with consistent colors
- **Clear call-to-action** button
- **Fallback text version** for accessibility

### Security Features
- **24-hour expiration** for verification links
- **Unique tokens** for each verification attempt
- **Clear security messaging** in emails

### Content Highlights
- **Portuguese language** throughout
- **Helpful instructions** and expectations
- **Contact information** for support

## 🧪 Testing

### Development Testing
```bash
# Start development server
pnpm dev

# Test newsletter signup
# 1. Go to homepage or any article
# 2. Fill out newsletter form
# 3. Check for success message
# 4. Look for email in Mailtrap inbox
# 5. Click verification link
# 6. Verify success page display
```

### Demo URLs for Testing
- Valid token: `/verify?token=abcdefgh12345678`
- Expired token: `/verify?token=expired`
- Invalid token: `/verify?token=invalid`

## 🔒 Security Considerations

### Token Security
- **Cryptographically secure** random token generation
- **Time-limited tokens** (24-hour expiration)
- **Single-use tokens** (cleared after verification)

### Email Security
- **Sender authentication** via Mailtrap
- **Rate limiting** handled by Convex
- **Input validation** on all email addresses

### Database Security
- **Indexed queries** for performance
- **Soft deletes** to maintain audit trail
- **No sensitive data exposure** in responses

## 🚀 Production Deployment

### Pre-deployment Checklist
- [ ] Convex is deployed and running
- [ ] Environment variables are set (including PUBLIC_CONVEX_URL)
- [ ] Mailtrap is configured with proper domain
- [ ] Email templates are tested
- [ ] Verification flow is tested end-to-end
- [ ] Error handling is verified
- [ ] Mobile responsiveness is confirmed

### Environment Setup
```env
# Production values
CONVEX_URL=https://your-prod-deployment.convex.cloud
PUBLIC_CONVEX_URL=https://your-prod-deployment.convex.cloud
MAILTRAP_TOKEN=your_production_token
MAILTRAP_SENDER_EMAIL=<EMAIL>
SITE_URL=https://yourdomain.com
```

## 🎯 Static Site Generation

The verification system is designed to work with static site generation:

- **Static verification page** at `/verify` (no dynamic routing needed)
- **Query parameter handling** for tokens (`/verify?token=xyz`)
- **Client-side Convex calls** for verification logic
- **Fallback simulation** when Convex is not available
- **Progressive enhancement** approach

## 🔧 Implementation Notes

### Why Query Parameters?
Using `/verify?token=xyz` instead of `/verify/[token]` because:
- **Static generation friendly** - no need for `getStaticPaths`
- **Simpler deployment** - works with any static hosting
- **Better caching** - single page cached everywhere
- **Cleaner fallbacks** - easier error handling

### Client-Side Architecture
- **Progressive enhancement** - page works without JavaScript
- **Graceful degradation** - simulation mode for development
- **Real-time feedback** - instant verification status
- **Accessibility first** - proper ARIA labels and focus management

*Built with ❤️ for secure, reliable newsletter management using modern serverless architecture* 