# 📧 Newsletter Functionality

This document describes the newsletter subscription system implemented for the Saúde Blog.

## 🎯 Overview

The newsletter system allows visitors to subscribe to receive updates about new articles, health insights, and medical technology content. It features a beautiful, responsive signup form with real-time validation and robust backend handling.

## ✨ Features

- **Beautiful UI**: Gradient design with emoji and feature badges
- **Real-time Validation**: Instant feedback as users type
- **Loading States**: Smooth animations during submission
- **Success Celebrations**: Pulse animation on successful signup
- **Error Handling**: Clear, helpful error messages
- **Mobile Responsive**: Perfect on all device sizes
- **Accessibility**: Keyboard navigation and screen reader support

## 📍 Locations

The newsletter signup form appears in two key locations:

1. **Homepage** (`src/pages/index.astro`) - Below recent posts, above "Explore More Content"
2. **Article Pages** (`src/layouts/PostDetails.astro`) - After share links, before post navigation

## 🛠 Technical Implementation

### Frontend Component (`src/components/Newsletter.astro`)
- Built with Astro and TypeScript
- Uses Tailwind CSS for styling
- Includes client-side validation and API integration
- Features loading states and success animations

### API Endpoint (`src/pages/api/newsletter/subscribe.ts`)
- RESTful POST endpoint at `/api/newsletter/subscribe`
- Validates email format and prevents spam
- Returns structured JSON responses
- Includes CORS headers for cross-origin requests

### Backend (Convex)
- **Schema** (`convex/schema.ts`): Defines subscriber table structure
- **Functions** (`convex/newsletter.ts`): Handles CRUD operations
- **Database**: Stores emails with timestamps and active status

## 🚀 Getting Started

### 1. Initialize Convex
```bash
npx convex dev
```

### 2. Set Environment Variables
Create `.env` file:
```env
CONVEX_URL=your_convex_deployment_url
```

### 3. Deploy Schema
```bash
npx convex deploy
```

### 4. Update API Connection
Replace the simulation in the API endpoint with actual Convex calls.

## 🎨 Customization

### Styling
The component uses your design system variables:
- `accent` - Primary brand color
- `background/foreground` - Text colors
- `border` - Form element borders
- `muted` - Subtle background colors

### Content
All text is in Portuguese and can be customized:
- Form title and description
- Placeholder text and button labels
- Success and error messages
- Feature badges

### Behavior
- Email validation patterns
- Success message duration
- Loading animation timing
- Error handling logic

## 📊 Database Schema

```typescript
newsletterSubscribers: {
  email: string,      // Subscriber email (unique)
  createdAt: number,  // Subscription timestamp
  isActive: boolean   // Active status (for soft deletes)
}
```

## 🔧 API Reference

### POST `/api/newsletter/subscribe`

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Inscrição realizada com sucesso!",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Response (400/409/500):**
```json
{
  "success": false,
  "message": "Error description",
  "error": "ERROR_CODE"
}
```

## 🧪 Testing

1. Start development server: `pnpm dev`
2. Navigate to homepage or any article
3. Find the newsletter signup form
4. Test with various email formats
5. Verify validation and success states

## 🔮 Future Enhancements

- **Email Service**: Integration with Resend/SendGrid for actual newsletters
- **Admin Dashboard**: Manage subscribers and send campaigns
- **Double Opt-in**: Email confirmation for new subscribers
- **Analytics**: Track signup rates and engagement
- **Preferences**: Allow subscribers to choose content types
- **Templates**: Beautiful email newsletter templates

## 🆘 Troubleshooting

**Form not submitting?**
- Check browser console for errors
- Verify API endpoint is accessible
- Test network connectivity

**Convex connection issues?**
- Confirm CONVEX_URL is set correctly
- Check if Convex dev server is running
- Verify schema deployment

**Styling problems?**
- Ensure Tailwind CSS is configured
- Check for CSS conflicts
- Verify design system variables

---

*Built with ❤️ for the Saúde Blog community* 