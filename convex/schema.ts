import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  newsletterSubscribers: defineTable({
    email: v.string(),
    createdAt: v.number(),
    isActive: v.boolean(),
    isVerified: v.boolean(),
    verificationToken: v.optional(v.string()),
    verificationTokenExpiresAt: v.optional(v.number()),
    verifiedAt: v.optional(v.number()),
    unsubscribeToken: v.optional(v.string()),
    unsubscribedAt: v.optional(v.number()),
  })
    .index("by_email", ["email"])
    .index("by_created_at", ["createdAt"])
    .index("by_verification_token", ["verificationToken"])
    .index("by_verified", ["isVerified"])
    .index("by_unsubscribe_token", ["unsubscribeToken"]),
}); 