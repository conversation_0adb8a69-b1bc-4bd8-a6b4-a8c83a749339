import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { internal } from "./_generated/api";

// Generate a secure random verification token
function generateVerificationToken(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Add a new newsletter subscriber (unverified initially)
export const subscribe = mutation({
  args: {
    email: v.string(),
  },
  handler: async (ctx, args) => {
    const { email } = args;
    
    // Check if email already exists
    const existingSubscriber = await ctx.db
      .query("newsletterSubscribers")
      .withIndex("by_email", (q) => q.eq("email", email))
      .first();

    if (existingSubscriber) {
      // If subscriber exists and is verified
      if (existingSubscriber.isVerified && existingSubscriber.isActive) {
        return { 
          success: false, 
          message: "Este email já está inscrito e verificado na newsletter.",
          code: "ALREADY_VERIFIED"
        };
      }
      
      // If subscriber exists but not verified, update verification token
      const verificationToken = generateVerificationToken();
      const expiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
      
      await ctx.db.patch(existingSubscriber._id, {
        verificationToken,
        verificationTokenExpiresAt: expiresAt,
        isActive: true,
      });

      return { 
        success: true, 
        message: "Um novo email de verificação foi enviado.",
        verificationToken,
        code: "VERIFICATION_RESENT"
      };
    }

    // Create new subscriber (unverified)
    const verificationToken = generateVerificationToken();
    const expiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 hours
    
    await ctx.db.insert("newsletterSubscribers", {
      email,
      createdAt: Date.now(),
      isActive: true,
      isVerified: false,
      verificationToken,
      verificationTokenExpiresAt: expiresAt,
    });

    return { 
      success: true, 
      message: "Verifique seu email para confirmar a inscrição.",
      verificationToken,
      code: "VERIFICATION_SENT"
    };
  },
});

// Verify email with token
export const verifyEmail = mutation({
  args: {
    token: v.string(),
  },
  handler: async (ctx, args) => {
    const { token } = args;
    
    const subscriber = await ctx.db
      .query("newsletterSubscribers")
      .withIndex("by_verification_token", (q) => q.eq("verificationToken", token))
      .first();

    if (!subscriber) {
      return { 
        success: false, 
        message: "Token de verificação inválido.",
        code: "INVALID_TOKEN"
      };
    }

    // Check if token has expired
    if (subscriber.verificationTokenExpiresAt && subscriber.verificationTokenExpiresAt < Date.now()) {
      return { 
        success: false, 
        message: "Token de verificação expirado. Solicite um novo email de verificação.",
        code: "TOKEN_EXPIRED"
      };
    }

    // Verify the email
    await ctx.db.patch(subscriber._id, {
      isVerified: true,
      verifiedAt: Date.now(),
      verificationToken: undefined,
      verificationTokenExpiresAt: undefined,
    });

    // Send welcome email after verification
    try {
      // @ts-expect-error - Action reference will be available after convex dev
      await ctx.scheduler.runAfter(0, internal.newsletter.sendWelcomeEmail, {
        email: subscriber.email,
      });
    } catch {
      // Welcome email failure shouldn't affect verification success
    }

    return { 
      success: true, 
      message: "Email verificado com sucesso! Bem-vindo à nossa newsletter.",
      email: subscriber.email,
      code: "EMAIL_VERIFIED"
    };
  },
});

// Get subscriber info by token (for verification page)
export const getSubscriberByToken = query({
  args: {
    token: v.string(),
  },
  handler: async (ctx, args) => {
    const { token } = args;
    
    const subscriber = await ctx.db
      .query("newsletterSubscribers")
      .withIndex("by_verification_token", (q) => q.eq("verificationToken", token))
      .first();

    if (!subscriber) {
      return null;
    }

    // Check if token has expired
    if (subscriber.verificationTokenExpiresAt && subscriber.verificationTokenExpiresAt < Date.now()) {
      return {
        email: subscriber.email,
        isExpired: true,
      };
    }

    return {
      email: subscriber.email,
      isExpired: false,
    };
  },
});

// Send verification email action
export const sendVerificationEmail = action({
  args: {
    email: v.string(),
    verificationToken: v.string(),
  },
  handler: async (ctx, args) => {
    const { email, verificationToken } = args;
    
    // Mailtrap API configuration
    const MAILTRAP_TOKEN = process.env.MAILTRAP_TOKEN;
    const MAILTRAP_SENDER_EMAIL = process.env.MAILTRAP_SENDER_EMAIL || "<EMAIL>";
    const SITE_URL = process.env.SITE_URL || "https://saudecomalex.com";
    
    if (!MAILTRAP_TOKEN) {
      throw new Error("MAILTRAP_TOKEN not configured");
    }

    const verificationUrl = `${SITE_URL}/verify?token=${verificationToken}`;
    
    const emailData = {
      from: {
        email: MAILTRAP_SENDER_EMAIL,
        name: "Saúde com Alex"
      },
      to: [{
        email: email
      }],
      subject: "Confirme sua inscrição na newsletter - Saúde com Alex",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Confirme sua inscrição</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
            .content { background: #f8fafc; padding: 30px; border-radius: 10px; margin: 20px 0; }
            .button { display: inline-block; background: #e6c9ff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 20px 0; }
            .button:hover { background: #1d4ed8; }
            .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #666; }
            .warning { background: #fef3cd; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #f59e0b; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">Saúde com Alex</div>
              <h1>Confirme sua inscrição</h1>
            </div>
            
            <div class="content">
              <p>Olá!</p>
              
              <p>Obrigado por se inscrever na newsletter <strong>Saúde com Alex</strong>! Para confirmar sua inscrição e começar a receber nossos conteúdos sobre saúde pública, medicina e tecnologias em saúde, clique no botão abaixo:</p>
              
              <div style="text-align: center;">
                <a href="${verificationUrl}" class="button">Confirmar Inscrição</a>
              </div>
              
              <p>Ou copie e cole este link no seu navegador:</p>
              <p style="word-break: break-all; background: #e5e7eb; padding: 10px; border-radius: 4px;">${verificationUrl}</p>
              
              <div class="warning">
                <strong>⏰ Importante:</strong> Este link expira em 24 horas. Se não conseguir confirmar a tempo, você pode tentar se inscrever novamente.
              </div>
              
              <p>Após a confirmação, você receberá:</p>
              <ul>
                <li>Artigos exclusivos sobre saúde pública</li>
                <li>Insights sobre tecnologias em saúde</li>
                <li>Reflexões sobre educação médica</li>
                <li>Novidades sobre o SUS e metodologias ativas</li>
              </ul>
            </div>
            
            <div class="footer">
              <p>Se você não se inscreveu para esta newsletter, pode ignorar este email com segurança.</p>
              <p>© 2024 Saúde com Alex - Compartilhando conhecimento em saúde</p>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
Confirme sua inscrição - Saúde com Alex

Olá!

Obrigado por se inscrever na newsletter Saúde com Alex! Para confirmar sua inscrição, acesse este link:

${verificationUrl}

Este link expira em 24 horas.

Após a confirmação, você receberá artigos exclusivos sobre saúde pública, tecnologias em saúde, educação médica e muito mais.

Se você não se inscreveu para esta newsletter, pode ignorar este email com segurança.

© 2024 Saúde com Alex
      `
    };

    try {
      const response = await fetch("https://send.api.mailtrap.io/api/send", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${MAILTRAP_TOKEN}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(emailData),
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Mailtrap API error: ${response.status} - ${errorData}`);
      }

      const result = await response.json();
      return {
        success: true,
        messageId: result.message_id,
      };
          } catch {
        throw new Error("Failed to send verification email");
      }
  },
});

// Subscribe with email verification (combines subscribe + send email)
export const subscribeWithVerification = action({
  args: {
    email: v.string(),
  },
  handler: async (ctx, args): Promise<{
    success: boolean;
    message: string;
    code?: string;
  }> => {
    const { email } = args;
    
    // First, create/update the subscription
    // @ts-expect-error - Types will be generated after convex dev
    const subscribeResult = await ctx.runMutation(internal.newsletter.subscribe, { email });
    
    if (!subscribeResult.success) {
      return subscribeResult;
    }

    // If we have a verification token, send the email
    if (subscribeResult.verificationToken) {
      try {
        // @ts-expect-error - Types will be generated after convex dev
        await ctx.runAction(internal.newsletter.sendVerificationEmail, {
          email,
          verificationToken: subscribeResult.verificationToken,
        });
        
        return {
          success: true,
          message: "Email de verificação enviado! Verifique sua caixa de entrada.",
          code: subscribeResult.code,
        };
             } catch {
         // Even if email fails, the subscription was created
         return {
           success: true,
           message: "Inscrição criada, mas houve um problema ao enviar o email. Tente novamente mais tarde.",
           code: "EMAIL_SEND_FAILED",
         };
       }
    }

    return subscribeResult;
  },
});

// Get all verified subscribers (for admin purposes)
export const getVerifiedSubscribers = query({
  handler: async (ctx) => {
    const subscribers = await ctx.db
      .query("newsletterSubscribers")
      .withIndex("by_verified", (q) => q.eq("isVerified", true))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    return subscribers.map(sub => ({
      email: sub.email,
      createdAt: sub.createdAt,
      verifiedAt: sub.verifiedAt,
    }));
  },
});

// Generate unsubscribe token
function generateUnsubscribeToken(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Send welcome email after verification
export const sendWelcomeEmail = action({
  args: {
    email: v.string(),
  },
  handler: async (ctx, args) => {
    const { email } = args;
    
    // Generate unsubscribe token
    const unsubscribeToken = generateUnsubscribeToken();
    
    // Store unsubscribe token in database
    // @ts-expect-error - Mutation reference will be available after convex dev
    await ctx.runMutation(internal.newsletter.storeUnsubscribeToken, {
      email,
      unsubscribeToken,
    });
    
    // Mailtrap API configuration
    const MAILTRAP_TOKEN = process.env.MAILTRAP_TOKEN;
    const MAILTRAP_SENDER_EMAIL = process.env.MAILTRAP_SENDER_EMAIL || "<EMAIL>";
    const SITE_URL = process.env.SITE_URL || "https://saudecomalex.com";
    
    if (!MAILTRAP_TOKEN) {
      throw new Error("MAILTRAP_TOKEN not configured");
    }

    const unsubscribeUrl = `${SITE_URL}/unsubscribe?token=${unsubscribeToken}`;
    
    const emailData = {
      from: {
        email: MAILTRAP_SENDER_EMAIL,
        name: "Saúde com Alex"
      },
      to: [{
        email: email
      }],
      subject: "Bem-vindo à Newsletter Saúde com Alex! 🎉",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Bem-vindo à nossa newsletter!</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
            .content { background: #f8fafc; padding: 30px; border-radius: 10px; margin: 20px 0; }
            .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 20px 0; }
            .button:hover { background: #1d4ed8; }
            .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #666; }
            .success { background: #dcfce7; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #22c55e; }
            .unsubscribe { margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">✉️ Saúde com Alex</div>
              <h1>Bem-vindo à nossa newsletter!</h1>
            </div>
            
            <div class="content">
              <div class="success">
                <strong>🎉 Inscrição confirmada com sucesso!</strong>
              </div>
              
              <p>Olá!</p>
              
              <p>Obrigado por se juntar à nossa comunidade! Sua inscrição na newsletter <strong>Saúde com Alex</strong> foi confirmada com sucesso.</p>
              
              <p>A partir de agora, você receberá:</p>
              <ul>
                <li><strong>Artigos exclusivos</strong> sobre saúde pública e medicina</li>
                <li><strong>Insights</strong> sobre tecnologias em saúde e inovação</li>
                <li><strong>Reflexões</strong> sobre educação médica e metodologias ativas</li>
                <li><strong>Novidades</strong> sobre o SUS e políticas de saúde</li>
                <li><strong>Conteúdos especiais</strong> disponíveis apenas para assinantes</li>
              </ul>
              
              <div style="text-align: center;">
                <a href="${SITE_URL}/posts/" class="button">Explorar Artigos Recentes</a>
              </div>
              
              <p>Estou muito animado para compartilhar conhecimento e aprender junto com você nesta jornada pela saúde pública!</p>
              
              <p>Um abraço,<br>
              <strong>Alexandre Borges Filho</strong><br>
              <em>Estudante de Medicina</em></p>
            </div>
            
            <div class="footer">
              <p>Você está recebendo este email porque confirmou sua inscrição na newsletter Saúde com Alex.</p>
              
              <div class="unsubscribe">
                <p>Não deseja mais receber nossos emails? <a href="${unsubscribeUrl}" style="color: #666; text-decoration: underline;">Cancelar inscrição</a></p>
                <p>© 2024 Saúde com Alex - Compartilhando conhecimento em saúde</p>
              </div>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
Bem-vindo à Newsletter Saúde com Alex!

Olá!

Obrigado por se juntar à nossa comunidade! Sua inscrição foi confirmada com sucesso.

A partir de agora, você receberá:
- Artigos exclusivos sobre saúde pública e medicina
- Insights sobre tecnologias em saúde e inovação
- Reflexões sobre educação médica e metodologias ativas
- Novidades sobre o SUS e políticas de saúde
- Conteúdos especiais disponíveis apenas para assinantes

Explore nossos artigos recentes: ${SITE_URL}/posts/

Estou muito animado para compartilhar conhecimento e aprender junto com você nesta jornada pela saúde pública!

Um abraço,
Alexandre Borges Filho
Estudante de Medicina

---
Não deseja mais receber nossos emails? Acesse: ${unsubscribeUrl}
© 2024 Saúde com Alex
      `
    };

    try {
      const response = await fetch("https://send.api.mailtrap.io/api/send", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${MAILTRAP_TOKEN}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(emailData),
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Mailtrap API error: ${response.status} - ${errorData}`);
      }

      const result = await response.json();
      return {
        success: true,
        messageId: result.message_id,
      };
    } catch {
      throw new Error("Failed to send welcome email");
    }
  },
});

// Store unsubscribe token for a subscriber
export const storeUnsubscribeToken = mutation({
  args: {
    email: v.string(),
    unsubscribeToken: v.string(),
  },
  handler: async (ctx, args) => {
    const { email, unsubscribeToken } = args;
    
    const subscriber = await ctx.db
      .query("newsletterSubscribers")
      .withIndex("by_email", (q) => q.eq("email", email))
      .first();

    if (subscriber) {
      await ctx.db.patch(subscriber._id, {
        unsubscribeToken,
      });
    }
  },
});

// Get subscriber by unsubscribe token
export const getSubscriberByUnsubscribeToken = query({
  args: {
    token: v.string(),
  },
  handler: async (ctx, args) => {
    const { token } = args;
    
    const subscriber = await ctx.db
      .query("newsletterSubscribers")
      .filter((q) => q.eq(q.field("unsubscribeToken"), token))
      .first();

    if (!subscriber) {
      return null;
    }

    return {
      email: subscriber.email,
      isActive: subscriber.isActive,
      isVerified: subscriber.isVerified,
    };
  },
});

// Unsubscribe a user with token
export const unsubscribeWithToken = mutation({
  args: {
    token: v.string(),
  },
  handler: async (ctx, args) => {
    const { token } = args;
    
    const subscriber = await ctx.db
      .query("newsletterSubscribers")
      .filter((q) => q.eq(q.field("unsubscribeToken"), token))
      .first();

    if (!subscriber) {
      return { 
        success: false, 
        message: "Token de cancelamento inválido.",
        code: "INVALID_TOKEN"
      };
    }

    if (!subscriber.isActive) {
      return { 
        success: false, 
        message: "Esta inscrição já foi cancelada anteriormente.",
        code: "ALREADY_UNSUBSCRIBED"
      };
    }

    await ctx.db.patch(subscriber._id, {
      isActive: false,
      unsubscribedAt: Date.now(),
      unsubscribeToken: undefined, // Clear token after use
    });

    return { 
      success: true, 
      message: "Inscrição cancelada com sucesso!",
      email: subscriber.email,
      code: "UNSUBSCRIBED"
    };
  },
});

// Unsubscribe a user by email (legacy method)
export const unsubscribe = mutation({
  args: {
    email: v.string(),
  },
  handler: async (ctx, args) => {
    const { email } = args;
    
    const subscriber = await ctx.db
      .query("newsletterSubscribers")
      .withIndex("by_email", (q) => q.eq("email", email))
      .first();

    if (!subscriber) {
      return { 
        success: false, 
        message: "Email não encontrado.",
        code: "EMAIL_NOT_FOUND"
      };
    }

    if (!subscriber.isActive) {
      return { 
        success: false, 
        message: "Esta inscrição já foi cancelada anteriormente.",
        code: "ALREADY_UNSUBSCRIBED"
      };
    }

    await ctx.db.patch(subscriber._id, {
      isActive: false,
      unsubscribedAt: Date.now(),
    });

    return { 
      success: true, 
      message: "Inscrição cancelada com sucesso!",
      code: "UNSUBSCRIBED"
    };
  },
}); 