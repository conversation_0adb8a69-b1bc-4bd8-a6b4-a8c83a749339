import type { Props } from "astro";
import IconMail from "@/assets/icons/IconMail.svg";
import IconGitHub from "@/assets/icons/IconGitHub.svg";
import IconBrandX from "@/assets/icons/IconBrandX.svg";
//import IconLinkedin from "@/assets/icons/IconLinkedin.svg";
import IconInstagram from "@/assets/icons/IconInstagram.svg";
import IconWhatsapp from "@/assets/icons/IconWhatsapp.svg";
import IconFacebook from "@/assets/icons/IconFacebook.svg";
import IconTelegram from "@/assets/icons/IconTelegram.svg";
import IconPinterest from "@/assets/icons/IconPinterest.svg";
import { SITE } from "@/config";

interface Social {
  name: string;
  href: string;
  linkTitle: string;
  icon: (_props: Props) => Element;
}

export const SOCIALS: Social[] = [
  //{
  //  name: "LinkedIn",
  //  href: "https://www.linkedin.com/in/username/",
  //  linkTitle: `${SITE.title} on LinkedIn`,
  //  icon: IconLinkedin,
  //},
  {
    name: "Instagram",
    href: "https://www.instagram.com/alex.bfilho/",
    linkTitle: `${SITE.title} no Instagram`,
    icon: IconInstagram,
  },
  {
    name: "Github",
    href: "https://github.com/alexandrenf/",
    linkTitle: `${SITE.title} no Github`,
    icon: IconGitHub,
  },
  {
    name: "Mail",
    href: "mailto:<EMAIL>",
    linkTitle: `Enviar um email para ${SITE.title}`,
    icon: IconMail,
  },
] as const;

export const SHARE_LINKS: Social[] = [
  {
    name: "WhatsApp",
    href: "https://wa.me/?text=",
    linkTitle: `Compartilhar no WhatsApp`,
    icon: IconWhatsapp,
  },
  {
    name: "Facebook",
    href: "https://www.facebook.com/sharer.php?u=",
    linkTitle: `Compartilhar no Facebook`,
    icon: IconFacebook,
  },
  {
    name: "X",
    href: "https://x.com/intent/post?url=",
    linkTitle: `Compartilhar no X`,
    icon: IconBrandX,
  },
  {
    name: "Telegram",
    href: "https://t.me/share/url?url=",
    linkTitle: `Compartilhar no Telegram`,
    icon: IconTelegram,
  },
  {
    name: "Pinterest",
    href: "https://pinterest.com/pin/create/button/?url=",
    linkTitle: `Compartilhar no Pinterest`,
    icon: IconPinterest,
  },
  {
    name: "Mail",
    href: "mailto:?subject=See%20this%20post&body=",
    linkTitle: `Compartilhar via email`,
    icon: IconMail,
  },
] as const;
