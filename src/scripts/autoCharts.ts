import { chartRegistry } from './chartRegistry';

/**
 * autoCharts scans for any custom element tag present in chartRegistry keys
 * and loads its module only once the element is (a) in DOM and (b) near viewport.
 */

const LOADED = new Set<string>();
const PENDING = new Set<Element>();

function loadForTag(tag: string) {
  if (LOADED.has(tag)) return;
  const loader = chartRegistry[tag];
  if (!loader) return;
  loader()
    .then(() => {
      LOADED.add(tag);
    })
    .catch(err => {
      const g = globalThis as unknown as Record<string, unknown>;
      if (g.__CHART_DEBUG__) {
        g.__chartLastError = err as unknown;
      }
    });
}

// Intersection observer to lazy load when element approaches viewport
const io = new IntersectionObserver(entries => {
  for (const entry of entries) {
    if (!entry.isIntersecting) continue;
    const el = entry.target;
    const tag = el.tagName.toLowerCase();
    io.unobserve(el);
    PENDING.delete(el);
    loadForTag(tag);
  }
}, { rootMargin: '600px 0px 600px 0px', threshold: 0 });

function scan(root: ParentNode = document) {
  const tags = Object.keys(chartRegistry);
  for (const tag of tags) {
    const found = root.querySelectorAll(tag);
    if (!found.length) continue;
    // If already loaded, skip observation
    if (LOADED.has(tag)) continue;
    found.forEach(el => {
      if (PENDING.has(el)) return;
      PENDING.add(el);
      io.observe(el);
    });
  }
}

// Initial scan after DOM ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => scan());
} else {
  scan();
}

// MutationObserver for dynamically injected charts
const mo = new MutationObserver(muts => {
  for (const m of muts) {
    if (m.type === 'childList') {
      m.addedNodes.forEach(node => {
        if (!(node instanceof Element)) return;
        scan(node);
      });
    }
  }
});
mo.observe(document.documentElement, { childList: true, subtree: true });

// Manual trigger API
window.addEventListener('charts-scan', () => scan());

// Ensure we rescan after Astro view transitions complete
document.addEventListener('astro:after-swap', () => scan());
