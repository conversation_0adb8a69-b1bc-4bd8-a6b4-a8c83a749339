import { init, graphic, use, type EChartsType } from "echarts/core";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "echarts/charts";
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  TitleComponent,
  MarkPointComponent,
} from "echarts/components";
import { <PERSON><PERSON><PERSON>enderer } from "echarts/renderers";
import { UniversalTransition } from "echarts/features";

use([
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  TitleComponent,
  MarkPointComponent,
  CanvasRenderer,
  UniversalTransition,
]);

type RawDatum = {
  year: number;
  individuals: number;
  registers: number;
  population: number;
};

type Palette = {
  accent: string;
  accentSoft: string;
  secondary: string;
  secondarySoft: string;
  foreground: string;
  background: string;
  muted: string;
};

const RAW_DATA: RawDatum[] = [
  { year: 1980, individuals: 113_495, registers: 124_232, population: 121_150_573 },
  { year: 1990, individuals: 131_278, registers: 143_697, population: 146_917_459 },
  { year: 2000, individuals: 205_296, registers: 224_717, population: 174_697_935 },
  { year: 2010, individuals: 304_406, registers: 333_203, population: 194_751_339 },
  { year: 2020, individuals: 480_882, registers: 526_373, population: 209_166_909 },
  { year: 2024, individuals: 597_428, registers: 653_945, population: 212_583_750 },
];

const FIGURE_TEMPLATE = `
  <figure class="medical-charts__card medical-charts__single">
    <header class="medical-charts__header">
      <div class="medical-charts__titles">
        <h3 id="chart-title">Evolução da força de trabalho médica</h3>
        <p id="chart-subtitle">Indivíduos, registros profissionais e população de 1980 a 2024*</p>
      </div>
      <div class="medical-charts__switch" role="tablist" aria-label="Alternar gráfico">
        <button type="button" role="tab" aria-selected="true" id="tab-trend" aria-controls="panel-trend" data-switch="trend" class="is-active">Força de trabalho</button>
        <button type="button" role="tab" aria-selected="false" id="tab-ratio" aria-controls="panel-ratio" data-switch="ratio">Médicos / 100 mil</button>
      </div>
    </header>
    <div class="medical-charts__panels">
      <div class="medical-charts__plot" data-chart="trend" id="panel-trend" role="tabpanel" aria-labelledby="tab-trend"></div>
      <div class="medical-charts__plot is-hidden" data-chart="ratio" id="panel-ratio" role="tabpanel" aria-labelledby="tab-ratio" hidden></div>
    </div>
    <figcaption>
      <p data-caption="trend">*O salto mais intenso acontece a partir de 2000, quando o total de médicos ativos cresce 192% até 2024, enquanto a população aumenta 22% no mesmo período.</p>
      <p data-caption="ratio" hidden>A densidade passa de 94 para 281 médicos por 100 mil habitantes. Hoje há quase 3 vezes mais profissionais disponíveis para cada habitante do que em 1980.</p>
    </figcaption>
  </figure>
`;

const STYLE_ID = "medical-charts-styles";

const ensureStyles = () => {
  if (document.getElementById(STYLE_ID)) return;
  const style = document.createElement("style");
  style.id = STYLE_ID;
  style.textContent = `
    .medical-charts__card {
      display: grid;
      gap: 0.75rem;
      padding: clamp(1.25rem, 3vw, 1.75rem);
      background: color-mix(in srgb, var(--background) 92%, var(--muted));
      border: 1px solid color-mix(in srgb, var(--border) 75%, transparent);
      border-radius: 1.25rem;
      box-shadow: 0 16px 28px rgba(15, 23, 42, 0.08);
      min-height: 100%;
    }
    .medical-charts__single { max-width: 1200px; margin-inline: auto; }
    .medical-charts__header h3 {
      font-size: clamp(1.1rem, 1.9vw, 1.35rem);
      font-weight: 700;
      margin: 0;
      color: var(--foreground);
    }
    .medical-charts__header p {
      margin: 0.25rem 0 0 0;
      font-size: 0.95rem;
      color: color-mix(in srgb, var(--foreground) 78%, var(--muted));
    }
    .medical-charts__plot {
      width: 100%;
      min-height: clamp(320px, 40vw, 420px);
      border-radius: 1rem;
      overflow: hidden;
      background: color-mix(in srgb, var(--background) 94%, var(--muted));
    }
    .medical-charts__panels { position: relative; }
    .medical-charts__plot.is-hidden { display: none; }
    .medical-charts__switch { display: flex; gap: 0.5rem; align-self: start; }
    .medical-charts__switch button {
      cursor: pointer;
      border: 1px solid color-mix(in srgb, var(--border) 75%, transparent);
      background: color-mix(in srgb, var(--background) 95%, var(--muted));
      color: var(--foreground);
      padding: 0.45rem 0.9rem;
      font-size: 0.78rem;
      line-height: 1;
      font-weight: 600;
      border-radius: 0.65rem;
      transition: background 0.25s, color 0.25s, box-shadow 0.25s;
    }
    .medical-charts__switch button:is(:hover,:focus-visible) {
      background: color-mix(in srgb, var(--background) 88%, var(--accent));
      outline: 0;
    }
    .medical-charts__switch button.is-active {
      background: var(--accent);
      color: var(--background);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    .medical-charts__header { display: flex; flex-direction: column; gap: 0.75rem; }
    @media (min-width: 680px) {
      .medical-charts__header { flex-direction: row; justify-content: space-between; align-items: flex-start; }
      .medical-charts__titles { max-width: 60%; }
    }
    @media (max-width: 639px) {
      .medical-charts__card { padding: 1rem 1.05rem 1.25rem; gap: 0.65rem; }
      .medical-charts__header h3 { font-size: 1rem; }
      .medical-charts__header p { font-size: 0.8rem; }
      .medical-charts__switch button { padding: 0.4rem 0.7rem; font-size: 0.7rem; }
      .medical-charts__plot { min-height: 300px; }
      .medical-charts__card figcaption { font-size: 0.78rem; }
    }
    .medical-charts__card figcaption {
      font-size: 0.92rem;
      color: color-mix(in srgb, var(--foreground) 72%, var(--muted));
      line-height: 1.5;
    }

    /* Removed two-column layout; now single switchable panel */
  `;
  document.head.append(style);
};

const getCssVar = (variable: string) => {
  const computed = getComputedStyle(document.documentElement);
  const value = computed.getPropertyValue(variable).trim();
  return value || undefined;
};

const clampChannel = (value: number) => Math.min(255, Math.max(0, Math.round(value)));

const hexToRgb = (hex: string) => {
  const raw = hex.replace("#", "").trim();
  if (![3, 6].includes(raw.length)) return null;
  const normalized = raw.length === 3 ? raw.split("").map(char => char + char).join("") : raw;
  const intVal = parseInt(normalized, 16);
  const r = (intVal >> 16) & 255;
  const g = (intVal >> 8) & 255;
  const b = intVal & 255;
  return { r, g, b };
};

const withAlpha = (hex: string, alpha: number) => {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${alpha})`;
};

const lighten = (hex: string, amount: number) => {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  const factor = amount / 100;
  return `#${[rgb.r, rgb.g, rgb.b]
    .map(channel => clampChannel(channel + (255 - channel) * factor)
      .toString(16)
      .padStart(2, "0"))
    .join("")}`;
};

const derivePalette = (): Palette => {
  const accent = getCssVar("--accent") ?? "#006cac";
  const foreground = getCssVar("--foreground") ?? "#282728";
  const background = getCssVar("--background") ?? "#fefefe";
  const muted = getCssVar("--muted") ?? "#e6e6e6";
  const secondary = lighten(accent, 35);
  return {
    accent,
    accentSoft: withAlpha(accent, 0.25),
    secondary,
    secondarySoft: withAlpha(lighten(accent, 55), 0.28),
    foreground,
    background,
    muted,
  };
};

const round = (value: number, precision = 1) =>
  Number.parseFloat(value.toFixed(precision));

const TREND_DATA = RAW_DATA.map(row => ({
  year: row.year,
  individuals: row.individuals,
  registers: row.registers,
  population: row.population,
}));

const RATIO_DATA = RAW_DATA.map(row => ({
  year: row.year,
  ratio: round((row.individuals / row.population) * 100_000, 1),
}));

const buildTrendOption = (palette: Palette, compact = false) => ({
  animationDuration: 1_200,
  animationEasing: "cubicOut" as const,
  animationDelay: (index: number) => index * 60,
  backgroundColor: palette.background,
  textStyle: {
    color: palette.foreground,
    fontFamily: "Peridot, system-ui, sans-serif",
  },
  legend: {
    top: compact ? 6 : 10,
    icon: "roundRect",
    itemWidth: compact ? 12 : 14,
    itemHeight: compact ? 7 : 8,
    itemGap: compact ? 12 : 18,
    textStyle: {
      color: palette.foreground,
      fontSize: compact ? 11 : 12,
    },
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
      shadowStyle: { color: withAlpha(palette.accent, 0.12) },
      lineStyle: { color: palette.accent },
    },
    backgroundColor: withAlpha(palette.background, 0.98),
    borderColor: palette.accent,
    borderWidth: 1,
    textStyle: {
      color: palette.foreground,
    },
    valueFormatter: (value: number) =>
      Number.isFinite(value) ? value.toLocaleString("pt-BR") : "",
  },
  grid: compact
    ? { top: 54, bottom: 48, left: 46, right: 40 }
    : { top: 60, bottom: 60, left: 60, right: 60 },
  xAxis: {
    type: "category",
    boundaryGap: false,
    axisLine: { lineStyle: { color: palette.muted } },
    axisTick: { show: false },
    axisLabel: { color: palette.foreground, fontWeight: 500 },
    data: TREND_DATA.map(item => item.year),
  },
  yAxis: [
    {
      type: "value",
      name: "Médicos",
      nameTextStyle: {
        color: palette.foreground,
        fontWeight: 600,
      },
      axisLabel: {
        color: palette.foreground,
        formatter: (value: number) =>
          Number(value).toLocaleString("pt-BR"),
      },
      splitLine: {
        lineStyle: {
          color: withAlpha(palette.muted, 0.45),
        },
      },
    },
    {
      type: "value",
      name: "População (milhões)",
      nameTextStyle: {
        color: palette.foreground,
        fontWeight: 600,
      },
      axisLabel: {
        color: palette.foreground,
        formatter: (value: number) => (value / 1_000_000).toFixed(0),
      },
      splitLine: { show: false },
    },
  ],
  series: [
    {
      name: "Médicos (indivíduos)",
      type: "line",
      smooth: true,
      showSymbol: true,
  symbolSize: compact ? 6 : 8,
  lineStyle: { width: compact ? 2.4 : 3, color: palette.accent },
      itemStyle: { color: palette.accent },
      areaStyle: {
        color: new graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: withAlpha(palette.accent, 0.28) },
          { offset: 1, color: withAlpha(palette.accent, 0.02) },
        ]),
      },
      emphasis: {
        focus: "series",
      },
      data: TREND_DATA.map(item => item.individuals),
      
    },
    {
      name: "Médicos (registros)",
      type: "line",
      yAxisIndex: 0,
      smooth: true,
      showSymbol: true,
  symbol: "rect",
  symbolSize: compact ? 7 : 9,
  lineStyle: { width: compact ? 2.2 : 2.6, color: palette.secondary },
      itemStyle: { color: palette.secondary },
      areaStyle: {
        color: new graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: withAlpha(palette.secondary, 0.2) },
          { offset: 1, color: withAlpha(palette.secondary, 0.02) },
        ]),
      },
      emphasis: {
        focus: "series",
      },
      data: TREND_DATA.map(item => item.registers),
    },
    {
      name: "População",
      type: "line",
      yAxisIndex: 1,
      smooth: true,
      showSymbol: false,
      lineStyle: { width: compact ? 2.4 : 3, type: "dashed", color: palette.muted },
      itemStyle: {
        color: palette.muted,
      },
      data: TREND_DATA.map(item => item.population),
    },
  ],
});

const buildRatioOption = (palette: Palette, compact = false) => ({
  animationDuration: 1200,
  animationEasing: "cubicInOut" as const,
  backgroundColor: palette.background,
  textStyle: {
    color: palette.foreground,
    fontFamily: "Peridot, system-ui, sans-serif",
  },
  grid: {
    top: compact ? 36 : 40,
    left: compact ? 44 : 60,
    right: compact ? 24 : 30,
    bottom: compact ? 54 : 60,
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    valueFormatter: (value: number) => `${value.toFixed(1)} médicos/100 mil`,
    backgroundColor: withAlpha(palette.background, 0.98),
    borderColor: palette.accent,
    borderWidth: 1,
    textStyle: { color: palette.foreground },
  },
  xAxis: {
    type: "category",
    axisTick: { show: false },
    axisLine: { lineStyle: { color: palette.muted } },
    axisLabel: { color: palette.foreground, fontSize: compact ? 11 : 12 },
    data: RATIO_DATA.map(item => item.year),
  },
  yAxis: {
    type: "value",
    axisLine: { show: false },
    axisTick: { show: false },
    splitLine: { lineStyle: { color: withAlpha(palette.muted, 0.45) } },
    axisLabel: {
      color: palette.foreground,
      formatter: (value: number) => `${value.toFixed(0)}`,
      fontSize: compact ? 11 : 12,
    },
  },
  legend: {
    show: false,
  },
  dataZoom: [
    {
      type: "slider",
      height: 12,
      start: 0,
      end: 100,
      bottom: 10,
      borderColor: withAlpha(palette.muted, 0.2),
      handleStyle: {
        color: palette.accent,
        borderWidth: 0,
      },
      brushSelect: false,
      fillerColor: withAlpha(palette.accent, 0.18),
    },
  ],
  series: [
    {
      type: "bar",
      cursor: "pointer",
  barWidth: compact ? "62%" : "55%",
      emphasis: { focus: "series" },
      itemStyle: {
        color: new graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: palette.accent },
          { offset: 0.6, color: palette.accentSoft },
          { offset: 1, color: palette.secondarySoft },
        ]),
        borderRadius: [12, 12, 6, 6],
        shadowColor: withAlpha(palette.accent, 0.25),
        shadowBlur: 18,
        shadowOffsetY: 8,
      },
      label: {
        show: true,
        position: "top",
        formatter: ({ value }: { value: number }) => `${value.toFixed(1)}`,
        color: palette.foreground,
        fontWeight: 600,
        fontSize: compact ? 11 : 12,
      },
      universalTransition: true,
      data: RATIO_DATA.map(item => item.ratio),
    },
  ],
});

class MedicalChartsElement extends HTMLElement {
  private trendChart?: EChartsType;
  private ratioChart?: EChartsType;
  private resizeObserver?: ResizeObserver;
  private themeObserver?: MutationObserver;
  private mounted = false;
  private active: 'trend' | 'ratio' = 'trend';
  private compact = false;

  connectedCallback() {
    if (this.mounted || typeof window === "undefined") return;
    this.mounted = true;
    ensureStyles();
    this.innerHTML = FIGURE_TEMPLATE;

    const trendTarget = this.querySelector<HTMLElement>("[data-chart=trend]");
    const ratioTarget = this.querySelector<HTMLElement>("[data-chart=ratio]");

    if (!trendTarget || !ratioTarget) return;

    trendTarget.innerHTML = "";
    ratioTarget.innerHTML = "";

    const palette = derivePalette();

    this.trendChart = init(trendTarget, undefined, { renderer: "canvas" });
    this.ratioChart = init(ratioTarget, undefined, { renderer: "canvas" });

    const computeCompact = () => {
      // Consider container width; fallback to window innerWidth
      const width = this.clientWidth || window.innerWidth;
      return width < 640; // breakpoint
    };
    this.compact = computeCompact();

    const loadingConfig = {
      text: "Carregando insights...",
      color: palette.accent,
      textColor: palette.foreground,
      maskColor: withAlpha(palette.background, 0.92),
      zlevel: 0,
    };

    this.trendChart.showLoading("default", loadingConfig);
    this.ratioChart.showLoading("default", loadingConfig);

    // Defer to ensure layout & computed styles are finalized
    requestAnimationFrame(() => {
      try {
        const trendOption = buildTrendOption(palette, this.compact);
        this.trendChart?.setOption(trendOption, { notMerge: true });
      } catch (err) {
        const g = globalThis as unknown as Record<string, unknown>;
        if (g.__MEDICAL_CHARTS_DEBUG__) {
          g.__medicalChartsLastError = err as unknown;
        }
      } finally {
        this.trendChart?.hideLoading();
      }
      try {
        const ratioOption = buildRatioOption(palette, this.compact);
        this.ratioChart?.setOption(ratioOption, { notMerge: true });
      } catch (err) {
        const g = globalThis as unknown as Record<string, unknown>;
        if (g.__MEDICAL_CHARTS_DEBUG__) {
          g.__medicalChartsLastError = err as unknown;
        }
      } finally {
        this.ratioChart?.hideLoading();
      }
      // Force an initial resize pass
      this.trendChart?.resize();
      this.ratioChart?.resize();
    });

    this.resizeObserver = new ResizeObserver(entries => {
      if (!Array.isArray(entries)) return;
      const nextCompact = (this.clientWidth || window.innerWidth) < 640;
      const compactChanged = nextCompact !== this.compact;
      this.compact = nextCompact;
      window.requestAnimationFrame(() => {
        if (compactChanged) {
          const paletteNext = derivePalette();
            this.trendChart?.setOption(buildTrendOption(paletteNext, this.compact), { notMerge: true });
            this.ratioChart?.setOption(buildRatioOption(paletteNext, this.compact), { notMerge: true });
        }
        this.trendChart?.resize();
        this.ratioChart?.resize();
      });
    });
    this.resizeObserver.observe(trendTarget);
    this.resizeObserver.observe(ratioTarget);

    this.themeObserver = new MutationObserver(mutations => {
      if (!mutations.some(({ attributeName }) => attributeName === "data-theme")) return;
      const nextPalette = derivePalette();
  const nextTrend = buildTrendOption(nextPalette, this.compact);
  const nextRatio = buildRatioOption(nextPalette, this.compact);
      // Fully refresh by not merging old option
      this.trendChart?.setOption(nextTrend, { notMerge: true, lazyUpdate: true });
      this.ratioChart?.setOption(nextRatio, { notMerge: true, lazyUpdate: true });
      // Defer resize to next frame to avoid main-process warning
      requestAnimationFrame(() => {
        this.trendChart?.resize();
        this.ratioChart?.resize();
      });
    });
    this.themeObserver.observe(document.documentElement, { attributes: true });

    // Setup switch logic
    const switcher = this.querySelector('.medical-charts__switch');
    const trendPanel = this.querySelector<HTMLElement>('#panel-trend');
    const ratioPanel = this.querySelector<HTMLElement>('#panel-ratio');
    const trendCaption = this.querySelector<HTMLElement>('p[data-caption=trend]');
    const ratioCaption = this.querySelector<HTMLElement>('p[data-caption=ratio]');
    const titleEl = this.querySelector<HTMLElement>('#chart-title');
    const subtitleEl = this.querySelector<HTMLElement>('#chart-subtitle');

    const applyState = (next: 'trend' | 'ratio') => {
      if (this.active === next) return;
      this.active = next;
      const buttons = switcher?.querySelectorAll('button[role=tab]');
      buttons?.forEach(btnEl => {
        const btn = btnEl as HTMLButtonElement;
        const isActive = btn.dataset.switch === next;
        btn.classList.toggle('is-active', isActive);
        btn.setAttribute('aria-selected', String(isActive));
        btn.tabIndex = isActive ? 0 : -1;
      });
      if (next === 'trend') {
        trendPanel?.classList.remove('is-hidden');
        trendPanel?.removeAttribute('hidden');
        ratioPanel?.classList.add('is-hidden');
        ratioPanel?.setAttribute('hidden', '');
        trendCaption?.removeAttribute('hidden');
        ratioCaption?.setAttribute('hidden', '');
        if (titleEl) titleEl.textContent = 'Evolução da força de trabalho médica';
        if (subtitleEl) subtitleEl.textContent = 'Indivíduos, registros profissionais e população de 1980 a 2024*';
        this.trendChart?.resize();
      } else {
        ratioPanel?.classList.remove('is-hidden');
        ratioPanel?.removeAttribute('hidden');
        trendPanel?.classList.add('is-hidden');
        trendPanel?.setAttribute('hidden', '');
        ratioCaption?.removeAttribute('hidden');
        trendCaption?.setAttribute('hidden', '');
        if (titleEl) titleEl.textContent = 'Quantos médicos por 100 mil habitantes?';
        if (subtitleEl) subtitleEl.textContent = 'Taxa de médicos (indivíduos) na população brasileira';
        this.ratioChart?.resize();
      }
    };

    switcher?.addEventListener('click', e => {
      const target = e.target as HTMLElement | null;
      if (!target || target.getAttribute('role') !== 'tab') return;
      const key = (target as HTMLButtonElement).dataset.switch;
      if (key === 'trend' || key === 'ratio') applyState(key);
    });

    switcher?.addEventListener('keydown', (e: Event) => {
      const ke = e as KeyboardEvent;
      const order: Array<'trend' | 'ratio'> = ['trend', 'ratio'];
      if (ke.key === 'ArrowRight' || ke.key === 'ArrowLeft') {
        ke.preventDefault();
        const idx = order.indexOf(this.active);
        const nextIdx = ke.key === 'ArrowRight' ? (idx + 1) % order.length : (idx - 1 + order.length) % order.length;
        const next = order[nextIdx];
        applyState(next);
        const btn = switcher.querySelector<HTMLButtonElement>(`button[data-switch=${next}]`);
        btn?.focus();
      }
    });
  }

  disconnectedCallback() {
    this.resizeObserver?.disconnect();
    this.themeObserver?.disconnect();
    this.resizeObserver = undefined;
    this.themeObserver = undefined;

    if (this.trendChart) {
      this.trendChart.dispose();
      this.trendChart = undefined;
    }
    if (this.ratioChart) {
      this.ratioChart.dispose();
      this.ratioChart = undefined;
    }
    this.mounted = false;
  }
}

if (!customElements.get("medical-charts")) {
  customElements.define("medical-charts", MedicalChartsElement);
}
