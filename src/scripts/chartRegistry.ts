// Central registry for custom chart elements -> dynamic import functions.
// Add new entries here to enable automatic loading.

export type ChartLoader = () => Promise<unknown>;

// Map tagName (always lowercase) to a lazy loader
export const chartRegistry: Record<string, ChartLoader> = {
  'medical-charts': () => import('./medicalCharts.ts'),
  'medical-gender-chart': () => import('./medicalGenderCharts.ts'),
  'medical-residency-chart': () => import('./medicalResidencyChart.ts'),
};

// Utility to register at runtime if needed (extension point)
export function registerChart(tag: string, loader: ChartLoader) {
  chartRegistry[tag.toLowerCase()] = loader;
}
