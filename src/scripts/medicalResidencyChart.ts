import { init, use, type EChartsType } from "echarts/core";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "echarts/charts";
import { GridComponent, TooltipComponent, LegendComponent, MarkLineComponent } from "echarts/components";
import { <PERSON><PERSON><PERSON>enderer } from "echarts/renderers";
import { UniversalTransition } from "echarts/features";

use([
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  GridComponent,
  Too<PERSON><PERSON><PERSON>omponent,
  <PERSON><PERSON><PERSON>ponent,
  <PERSON><PERSON><PERSON>Component,
  CanvasRenderer,
  UniversalTransition,
]);

type ResidencyDatum = {
  year: number;
  authorized: number;
  filled: number;
  unfilled: number;
  vacancyRate: number;
};

const RAW_DATA: ResidencyDatum[] = [
  { year: 2018, authorized: 22_138, filled: 16_302, unfilled: 5_836, vacancyRate: 26.4 },
  { year: 2019, authorized: 22_406, filled: 16_796, unfilled: 5_610, vacancyRate: 25.0 },
  { year: 2020, authorized: 22_346, filled: 16_858, unfilled: 5_488, vacancyRate: 24.6 },
  { year: 2021, authorized: 22_968, filled: 16_594, unfilled: 6_374, vacancyRate: 27.8 },
  { year: 2022, authorized: 22_979, filled: 17_417, unfilled: 5_562, vacancyRate: 24.2 },
  { year: 2023, authorized: 23_526, filled: 18_272, unfilled: 5_254, vacancyRate: 22.3 },
  { year: 2024, authorized: 24_210, filled: 19_551, unfilled: 4_659, vacancyRate: 19.2 },
];

const YEARS = RAW_DATA.map(row => row.year);
const AUTHORIZED = RAW_DATA.map(row => row.authorized);
const FILLED = RAW_DATA.map(row => row.filled);
const UNFILLED = RAW_DATA.map(row => row.unfilled);
const VACANCY_RATE = RAW_DATA.map(row => row.vacancyRate);

const MAX_VACANCY_RATE = Math.max(...VACANCY_RATE);
const VACANCY_AXIS_MAX = Math.ceil((MAX_VACANCY_RATE + 2) / 5) * 5;

const STYLE_ID = "medical-residency-chart-styles";

const FIGURE_TEMPLATE = `
  <figure class="medical-residency__card">
    <header class="medical-residency__header">
      <div class="medical-residency__titles">
        <h3 id="residency-chart-title">Vagas de R1, nem todas ocupadas</h3>
        <p id="residency-chart-subtitle">Residência médica — vagas autorizadas, ocupadas e não preenchidas (2018-2024)</p>
      </div>
    </header>
    <div class="medical-residency__plot" data-chart="main" role="img" aria-labelledby="residency-chart-title residency-chart-subtitle"></div>
    <figcaption>
      Fonte: Elaboração dos autores; CNRM/Sesu/MEC. Nota: Considera vagas de R1 em acesso direto e pré-requisito. Para citação, atribuir a Scheffer (2025).
    </figcaption>
  </figure>
`;

const ensureStyles = () => {
  if (document.getElementById(STYLE_ID)) return;
  const style = document.createElement("style");
  style.id = STYLE_ID;
  style.textContent = `
    .medical-residency__card {
      display: grid;
      gap: 0.75rem;
      padding: clamp(1.25rem, 3vw, 1.75rem);
      background: color-mix(in srgb, var(--background) 92%, var(--muted));
      border: 1px solid color-mix(in srgb, var(--border) 75%, transparent);
      border-radius: 1.25rem;
      box-shadow: 0 16px 28px rgba(15, 23, 42, 0.08);
      min-height: 100%;
    }
    .medical-residency__header h3 {
      font-size: clamp(1.1rem, 1.9vw, 1.35rem);
      font-weight: 700;
      margin: 0;
      color: var(--foreground);
    }
    .medical-residency__header p {
      margin: 0.25rem 0 0 0;
      font-size: 0.95rem;
      color: color-mix(in srgb, var(--foreground) 78%, var(--muted));
    }
    .medical-residency__plot {
      width: 100%;
      min-height: clamp(320px, 38vw, 420px);
      border-radius: 1rem;
      overflow: hidden;
      background: color-mix(in srgb, var(--background) 94%, var(--muted));
    }
    .medical-residency__header { display: flex; flex-direction: column; gap: 0.75rem; }
    .medical-residency__titles { max-width: 60ch; }
    @media (min-width: 680px) {
      .medical-residency__header { flex-direction: row; justify-content: space-between; align-items: flex-start; }
      .medical-residency__titles { max-width: 60%; }
    }
    @media (max-width: 639px) {
      .medical-residency__card { padding: 1rem 1.05rem 1.25rem; gap: 0.65rem; }
      .medical-residency__header h3 { font-size: 1rem; }
      .medical-residency__header p { font-size: 0.8rem; }
      .medical-residency__plot { min-height: 300px; }
      .medical-residency__card figcaption { font-size: 0.78rem; }
    }
    @media (max-width: 479px) {
      .medical-residency__card {
        padding: 0.85rem 0.9rem 1rem;
        gap: 0.5rem;
        margin: 0 -0.5rem;
      }
      .medical-residency__header h3 {
        font-size: 0.95rem;
        line-height: 1.3;
      }
      .medical-residency__header p {
        font-size: 0.75rem;
        line-height: 1.4;
        margin-top: 0.15rem;
      }
      .medical-residency__plot {
        min-height: 280px;
        border-radius: 0.75rem;
      }
      .medical-residency__card figcaption {
        font-size: 0.72rem;
        line-height: 1.4;
      }
    }
    .medical-residency__card figcaption {
      font-size: 0.92rem;
      color: color-mix(in srgb, var(--foreground) 72%, var(--muted));
      line-height: 1.5;
    }
  `;
  document.head.append(style);
};

const getCssVar = (variable: string) => {
  const computed = getComputedStyle(document.documentElement);
  const value = computed.getPropertyValue(variable).trim();
  return value || undefined;
};

const clampChannel = (value: number) => Math.min(255, Math.max(0, Math.round(value)));

const hexToRgb = (hex: string) => {
  const raw = hex.replace("#", "").trim();
  if (![3, 6].includes(raw.length)) return null;
  const normalized = raw.length === 3 ? raw.split("").map(char => char + char).join("") : raw;
  const intVal = Number.parseInt(normalized, 16);
  const r = (intVal >> 16) & 255;
  const g = (intVal >> 8) & 255;
  const b = intVal & 255;
  return { r, g, b };
};

const withAlpha = (hex: string, alpha: number) => {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${alpha})`;
};

// Removed lighten/darken helpers as we simplified the palette

const colorMix = (hexA: string, hexB: string) => {
  const rgbA = hexToRgb(hexA);
  const rgbB = hexToRgb(hexB);
  if (!rgbA || !rgbB) return hexA;
  const mixChannel = (channel: keyof typeof rgbA) =>
    clampChannel((rgbA[channel] + rgbB[channel]) / 2).toString(16).padStart(2, "0");
  return `#${mixChannel("r")}${mixChannel("g")}${mixChannel("b")}`;
};

type Palette = {
  accent: string;
  accentSoft: string;
  secondary: string;
  secondarySoft: string;
  vacancyLine: string;
  foreground: string;
  background: string;
  muted: string;
};

// Minimal type for ECharts tooltip formatter parameter we rely on
type AxisFormatterParam = { dataIndex?: number } | Array<{ dataIndex?: number }>;

const derivePalette = (): Palette => {
  const accent = getCssVar("--accent") ?? "#006cac";
  const foreground = getCssVar("--foreground") ?? "#242728";
  const background = getCssVar("--background") ?? "#fefefe";
  const muted = getCssVar("--muted") ?? "#dddddd";
  // Use a clearer neutral slate tone for unfilled bars for better legibility across themes
  const secondary = "#94a3b8"; // slate-400
  const vacancyLine = colorMix(accent, "#f97316");
  return {
    accent,
    accentSoft: withAlpha(accent, 0.22),
    secondary,
    secondarySoft: withAlpha(secondary, 0.25),
    vacancyLine,
    foreground,
    background,
    muted,
  };
};

const formatCount = (value: number) =>
  Number(value).toLocaleString("pt-BR");

const formatPercent = (value: number) =>
  `${Number(value).toLocaleString("pt-BR", { minimumFractionDigits: 1, maximumFractionDigits: 1 })}%`;

const buildOption = (palette: Palette, compact = false, extraCompact = false) => ({
  animationDuration: 1_000,
  animationEasing: "cubicOut" as const,
  backgroundColor: palette.background,
  textStyle: {
    color: palette.foreground,
    fontFamily: "Peridot, system-ui, sans-serif",
  },
  legend: {
    top: extraCompact ? 2 : compact ? 4 : 8,
    icon: "roundRect",
    itemWidth: extraCompact ? 9 : compact ? 11 : 13,
    itemHeight: extraCompact ? 5 : compact ? 6 : 7,
    itemGap: extraCompact ? 8 : compact ? 10 : 16,
    textStyle: {
      color: palette.foreground,
      fontSize: extraCompact ? 9 : compact ? 10 : 11.5,
    },
    // Improve legend layout for mobile
    orient: extraCompact ? "horizontal" : "horizontal",
    left: "center",
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
      shadowStyle: { color: withAlpha(palette.accent, 0.12) },
      lineStyle: { color: palette.accent },
    },
    backgroundColor: withAlpha(palette.background, 0.96),
    borderColor: withAlpha(palette.foreground, 0.2),
    borderWidth: 1,
    textStyle: {
      color: palette.foreground,
      fontSize: extraCompact ? 11 : compact ? 12 : 13,
    },
    // Mobile-optimized tooltip with better spacing and readability
    formatter: (params: AxisFormatterParam) => {
      const first = Array.isArray(params) ? params[0] : params;
      const i = first?.dataIndex ?? 0;
      const year = YEARS[i];
      const filled = FILLED[i];
      const unfilled = UNFILLED[i];
      const authorized = AUTHORIZED[i] ?? (filled + unfilled);
      const vacancy = VACANCY_RATE[i];

      if (extraCompact) {
        // Compact tooltip for very small screens
        return [
          `<div style="font-weight:700;margin-bottom:6px;font-size:13px">${year}</div>`,
          `<div style="margin-bottom:2px">Autorizadas: <b>${formatCount(authorized)}</b></div>`,
          `<div style="margin-bottom:2px">Ocupadas: <b>${formatCount(filled)}</b></div>`,
          `<div style="margin-bottom:2px">Não ocupadas: <b>${formatCount(unfilled)}</b></div>`,
          `<div>% não ocupadas: <b>${formatPercent(vacancy)}</b></div>`,
        ].join("");
      }

      return [
        `<div style="font-weight:700;margin-bottom:4px">${year}</div>`,
        `<div>Autorizadas: <b>${formatCount(authorized)}</b></div>`,
        `<div>Ocupadas: <b>${formatCount(filled)}</b></div>`,
        `<div>Não ocupadas: <b>${formatCount(unfilled)}</b></div>`,
        `<div>% não ocupadas: <b>${formatPercent(vacancy)}</b></div>`,
      ].join("");
    },
    // Better positioning for mobile
    position: extraCompact ? "top" : undefined,
  },
  grid: extraCompact
    ? { top: 50, left: 42, right: 50, bottom: 50 }
    : compact
    ? { top: 60, left: 48, right: 56, bottom: 56 }
    : { top: 68, left: 64, right: 88, bottom: 64 },
  xAxis: {
    type: "category" as const,
    data: YEARS,
    axisLine: { lineStyle: { color: withAlpha(palette.muted, 0.6) } },
    axisTick: { show: false },
    axisLabel: {
      color: palette.foreground,
      fontSize: extraCompact ? 10 : compact ? 11 : 12,
      // Better spacing for mobile
      margin: extraCompact ? 6 : compact ? 8 : 10,
    },
  },
  yAxis: [
    {
      type: "value" as const,
      name: "Vagas",
      nameGap: extraCompact ? 28 : compact ? 32 : 40,
      nameTextStyle: {
        color: palette.foreground,
        fontWeight: 600,
        fontSize: extraCompact ? 11 : compact ? 12 : 13
      },
      axisLabel: {
        color: palette.foreground,
        formatter: (value: number) => {
          // Shorter format for mobile to prevent overlap
          if (extraCompact && value >= 1000) {
            return `${Math.round(value / 1000)}k`;
          }
          return formatCount(value);
        },
        fontSize: extraCompact ? 10 : compact ? 11 : 12,
        margin: extraCompact ? 4 : compact ? 6 : 8,
      },
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { lineStyle: { color: withAlpha(palette.muted, 0.4) } },
    },
    {
      type: "value" as const,
      name: extraCompact ? "% não preench." : "% não preenchidas",
      position: "right",
      min: 0,
      max: VACANCY_AXIS_MAX,
      nameGap: extraCompact ? 32 : compact ? 36 : 44,
      nameTextStyle: {
        color: palette.foreground,
        fontWeight: 600,
        fontSize: extraCompact ? 11 : compact ? 12 : 13
      },
      axisLabel: {
        color: palette.foreground,
        formatter: (value: number) => formatPercent(value),
        fontSize: extraCompact ? 10 : compact ? 11 : 12,
        margin: extraCompact ? 4 : compact ? 6 : 8,
      },
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false },
    },
  ],
  series: [
    {
      name: "Ocupadas",
      type: "bar" as const,
      stack: "vagas",
      barWidth: extraCompact ? 14 : compact ? 18 : 24,
      itemStyle: {
        borderRadius: [8, 8, 0, 0],
        color: palette.accent,
      },
      emphasis: {
        focus: "series",
        // Better touch interaction for mobile
        itemStyle: {
          shadowBlur: extraCompact ? 8 : 10,
          shadowColor: withAlpha(palette.accent, 0.3),
        }
      },
      data: FILLED,
      tooltip: { valueFormatter: (value: number) => formatCount(value) },
    },
    {
      name: "Não ocupadas",
      type: "bar" as const,
      stack: "vagas",
      barWidth: extraCompact ? 14 : compact ? 18 : 24,
      itemStyle: {
        borderRadius: [8, 8, 0, 0],
        color: palette.secondary,
      },
      emphasis: {
        focus: "series",
        // Better touch interaction for mobile
        itemStyle: {
          shadowBlur: extraCompact ? 8 : 10,
          shadowColor: withAlpha(palette.secondary, 0.3),
        }
      },
      data: UNFILLED,
      tooltip: { valueFormatter: (value: number) => formatCount(value) },
    },
    {
      name: extraCompact ? "% não ocup." : "% não ocupadas",
      type: "line" as const,
      yAxisIndex: 1,
      smooth: true,
      symbol: "diamond",
      symbolSize: extraCompact ? 4 : compact ? 5 : 7,
      lineStyle: {
        width: extraCompact ? 1.5 : compact ? 1.8 : 2.2,
        color: palette.vacancyLine
      },
      itemStyle: { color: palette.vacancyLine },
      // areaStyle intentionally omitted to keep the line light
      emphasis: {
        focus: "series",
        // Better touch interaction for mobile
        lineStyle: {
          width: extraCompact ? 2.5 : compact ? 3 : 3.5,
        },
        itemStyle: {
          shadowBlur: extraCompact ? 6 : 8,
          shadowColor: withAlpha(palette.vacancyLine, 0.4),
        }
      },
      data: VACANCY_RATE,
      tooltip: { valueFormatter: (value: number) => formatPercent(value) },
    },
  ],
});

class MedicalResidencyChartElement extends HTMLElement {
  private chart?: EChartsType;
  private resizeObserver?: ResizeObserver;
  private themeObserver?: MutationObserver;
  private mounted = false;
  private compact = false;
  private extraCompact = false;

  connectedCallback() {
    if (this.mounted || typeof window === "undefined") return;
    this.mounted = true;

    ensureStyles();
    this.innerHTML = FIGURE_TEMPLATE;

    const target = this.querySelector<HTMLElement>("[data-chart=main]");
    if (!target) return;

    const palette = derivePalette();
    this.chart = init(target, undefined, { renderer: "canvas" });

    const computeResponsiveMode = () => {
      const width = this.clientWidth || window.innerWidth;
      return {
        extraCompact: width < 480, // Very small screens (phones in portrait)
        compact: width < 640,      // Small screens (phones in landscape, small tablets)
      };
    };

    const responsiveMode = computeResponsiveMode();
    this.compact = responsiveMode.compact;
    this.extraCompact = responsiveMode.extraCompact;

    const loadingConfig = {
      text: "Carregando dados...",
      color: palette.accent,
      textColor: palette.foreground,
      maskColor: withAlpha(palette.background, 0.92),
      zlevel: 0,
    };

    this.chart.showLoading("default", loadingConfig);

    requestAnimationFrame(() => {
      try {
        this.chart?.setOption(buildOption(palette, this.compact, this.extraCompact), { notMerge: true });
      } catch (error) {
        const global = globalThis as unknown as Record<string, unknown>;
        if (global.__MEDICAL_RESIDENCY_DEBUG__) {
          global.__medicalResidencyLastError = error as unknown;
        }
      } finally {
        this.chart?.hideLoading();
        this.chart?.resize();
      }
    });

    this.resizeObserver = new ResizeObserver(() => {
      const nextResponsiveMode = computeResponsiveMode();
      const changed = nextResponsiveMode.compact !== this.compact ||
                     nextResponsiveMode.extraCompact !== this.extraCompact;
      this.compact = nextResponsiveMode.compact;
      this.extraCompact = nextResponsiveMode.extraCompact;
      window.requestAnimationFrame(() => {
        if (changed) {
          const nextPalette = derivePalette();
          this.chart?.setOption(buildOption(nextPalette, this.compact, this.extraCompact), { notMerge: true });
        }
        this.chart?.resize();
      });
    });
    this.resizeObserver.observe(this);

    this.themeObserver = new MutationObserver(mutations => {
      if (!mutations.some(({ attributeName }) => attributeName === "data-theme")) return;
      const nextPalette = derivePalette();
      this.chart?.setOption(buildOption(nextPalette, this.compact, this.extraCompact), { notMerge: true, lazyUpdate: true });
      requestAnimationFrame(() => {
        this.chart?.resize();
      });
    });
    this.themeObserver.observe(document.documentElement, { attributes: true });
  }

  disconnectedCallback() {
    this.resizeObserver?.disconnect();
    this.themeObserver?.disconnect();
    this.resizeObserver = undefined;
    this.themeObserver = undefined;

    if (this.chart) {
      this.chart.dispose();
      this.chart = undefined;
    }
    this.mounted = false;
  }
}

if (!customElements.get("medical-residency-chart")) {
  customElements.define("medical-residency-chart", MedicalResidencyChartElement);
}

export {};
