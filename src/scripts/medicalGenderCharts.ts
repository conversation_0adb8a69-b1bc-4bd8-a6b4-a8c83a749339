import { init, graphic, use, type EChartsType } from "echarts/core";
import { LineChart } from "echarts/charts";
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
  Mark<PERSON>reaComponent,
  MarkLineComponent,
} from "echarts/components";
import { <PERSON>vasRenderer } from "echarts/renderers";
import { UniversalTransition } from "echarts/features";

use([
  <PERSON><PERSON><PERSON>,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
  <PERSON><PERSON><PERSON><PERSON>omponent,
  <PERSON><PERSON><PERSON><PERSON>omponent,
  CanvasRenderer,
  UniversalTransition,
]);

type GenderDatum = {
  year: number;
  female: number;
  male: number;
  total: number;
  femaleShare: number;
  maleShare: number;
  projection: boolean;
};

const RAW_DATA: GenderDatum[] = [
  { year: 2009, female: 118_468, femaleShare: 40.5, male: 174_326, maleShare: 59.5, total: 292_794, projection: false },
  { year: 2010, female: 124_748, femaleShare: 41.0, male: 179_658, maleShare: 59.0, total: 304_406, projection: false },
  { year: 2011, female: 133_431, femaleShare: 41.7, male: 186_439, maleShare: 58.3, total: 319_870, projection: false },
  { year: 2012, female: 141_745, femaleShare: 42.4, male: 192_931, maleShare: 57.6, total: 334_676, projection: false },
  { year: 2013, female: 151_072, femaleShare: 43.0, male: 199_896, maleShare: 57.0, total: 350_968, projection: false },
  { year: 2014, female: 160_896, femaleShare: 43.7, male: 207_226, maleShare: 56.3, total: 368_122, projection: false },
  { year: 2015, female: 170_448, femaleShare: 44.3, male: 214_396, maleShare: 55.7, total: 384_844, projection: false },
  { year: 2016, female: 180_495, femaleShare: 44.9, male: 221_612, maleShare: 55.1, total: 402_107, projection: false },
  { year: 2017, female: 191_129, femaleShare: 45.5, male: 229_178, maleShare: 54.5, total: 420_307, projection: false },
  { year: 2018, female: 202_866, femaleShare: 46.1, male: 237_222, maleShare: 53.9, total: 440_088, projection: false },
  { year: 2019, female: 213_366, femaleShare: 46.6, male: 244_062, maleShare: 53.4, total: 457_428, projection: false },
  { year: 2020, female: 227_188, femaleShare: 47.2, male: 253_694, maleShare: 52.8, total: 480_882, projection: false },
  { year: 2021, female: 243_012, femaleShare: 47.9, male: 264_135, maleShare: 52.1, total: 507_147, projection: false },
  { year: 2022, female: 261_456, femaleShare: 48.6, male: 276_643, maleShare: 51.4, total: 538_099, projection: false },
  { year: 2023, female: 278_394, femaleShare: 48.6, male: 294_566, maleShare: 51.4, total: 572_960, projection: false },
  { year: 2024, female: 294_760, femaleShare: 49.3, male: 302_668, maleShare: 50.7, total: 597_428, projection: true },
  { year: 2025, female: 323_730, femaleShare: 50.9, male: 311_976, maleShare: 49.1, total: 635_706, projection: true },
  { year: 2026, female: 348_298, femaleShare: 51.6, male: 326_343, maleShare: 48.4, total: 674_641, projection: true },
  { year: 2027, female: 373_373, femaleShare: 52.3, male: 340_281, maleShare: 47.7, total: 713_654, projection: true },
  { year: 2028, female: 388_666, femaleShare: 51.6, male: 364_167, maleShare: 48.4, total: 752_833, projection: true },
  { year: 2029, female: 416_612, femaleShare: 52.3, male: 379_687, maleShare: 47.7, total: 796_299, projection: true },
  { year: 2030, female: 449_525, femaleShare: 53.0, male: 398_819, maleShare: 47.0, total: 848_344, projection: true },
  { year: 2031, female: 487_737, femaleShare: 53.6, male: 421_681, maleShare: 46.4, total: 909_418, projection: true },
  { year: 2032, female: 526_355, femaleShare: 54.2, male: 444_016, maleShare: 45.8, total: 970_371, projection: true },
  { year: 2033, female: 564_883, femaleShare: 54.8, male: 465_856, maleShare: 45.2, total: 1_030_739, projection: true },
  { year: 2034, female: 603_310, femaleShare: 55.3, male: 488_064, maleShare: 44.7, total: 1_091_374, projection: true },
  { year: 2035, female: 641_809, femaleShare: 55.7, male: 510_421, maleShare: 44.3, total: 1_152_230, projection: true },
];

const PROJECTION_START = 2024;
const PROJECTION_END = 2035;

const DATA_BY_YEAR = new Map(RAW_DATA.map(row => [row.year, row]));

const YEARS = RAW_DATA.map(row => row.year);
const FEMALE_COUNTS = RAW_DATA.map(row => row.female);
const MALE_COUNTS = RAW_DATA.map(row => row.male);
const TOTAL_COUNTS = RAW_DATA.map(row => row.total);
const TOTAL_DELTA: number[] = RAW_DATA.map((row, index, list) =>
  index === 0 ? 0 : row.total - list[index - 1].total,
);
const FEMALE_SHARE = RAW_DATA.map(row => row.femaleShare);
const MALE_SHARE = RAW_DATA.map(row => row.maleShare);

const STYLE_ID = "medical-gender-chart-styles";

const FIGURE_TEMPLATE = `
  <figure class="medical-gender__card">
    <header class="medical-gender__header">
      <div class="medical-gender__titles">
        <h3 id="gender-chart-title">Mulheres puxam o crescimento da categoria</h3>
        <p id="gender-chart-subtitle">Número de médicos por sexo — Brasil (2009-2035)</p>
      </div>
      <div class="medical-gender__controls">
        <div class="medical-gender__switch" role="tablist" aria-label="Alternar visualização">
          <button type="button" role="tab" aria-selected="true" id="gender-tab-counts" aria-controls="gender-panel-counts" data-switch="counts" class="is-active">Totais</button>
          <button type="button" role="tab" aria-selected="false" id="gender-tab-share" aria-controls="gender-panel-share" data-switch="share">Participação %</button>
        </div>
      </div>
    </header>
    <div class="medical-gender__panels">
      <div class="medical-gender__plot" data-chart="counts" id="gender-panel-counts" role="tabpanel" aria-labelledby="gender-tab-counts"></div>
      <div class="medical-gender__plot is-hidden" data-chart="share" id="gender-panel-share" role="tabpanel" aria-labelledby="gender-tab-share" hidden></div>
      <button type="button" class="medical-gender__growth-toggle" data-derivative-toggle aria-pressed="false" aria-label="Ativar série de crescimento" hidden>Ver crescimento</button>
    </div>
    <figcaption>
      <p data-caption="counts">O total de médicos pode chegar a 1,15 milhão em 2035. A expansão é puxada pelas mulheres, que mais do que dobram desde 2013.</p>
      <p data-caption="share" hidden>As mulheres devem ultrapassar 55% da categoria até 2035; a paridade acontece já em 2025.</p>
    </figcaption>
  </figure>
`;

const ensureStyles = () => {
  if (document.getElementById(STYLE_ID)) return;
  const style = document.createElement("style");
  style.id = STYLE_ID;
  style.textContent = `
    .medical-gender__card {
      display: grid;
      gap: 0.75rem;
      padding: clamp(1.25rem, 3vw, 1.75rem);
      background: color-mix(in srgb, var(--background) 92%, var(--muted));
      border: 1px solid color-mix(in srgb, var(--border) 72%, transparent);
      border-radius: 1.25rem;
      box-shadow: 0 16px 28px rgba(15, 23, 42, 0.08);
      min-height: 100%;
    }
    .medical-gender__header h3 {
      font-size: clamp(1.1rem, 1.9vw, 1.35rem);
      font-weight: 700;
      margin: 0;
      color: var(--foreground);
    }
    .medical-gender__header p {
      margin: 0.25rem 0 0 0;
      font-size: 0.95rem;
      color: color-mix(in srgb, var(--foreground) 78%, var(--muted));
    }
    .medical-gender__plot {
      width: 100%;
      min-height: clamp(320px, 40vw, 420px);
      border-radius: 1rem;
      overflow: hidden;
      background: color-mix(in srgb, var(--background) 94%, var(--muted));
    }
    .medical-gender__panels { position: relative; }
    .medical-gender__plot.is-hidden { display: none; }
    .medical-gender__controls { display: flex; flex-wrap: wrap; gap: 0.5rem; align-items: center; justify-content: flex-start; }
    .medical-gender__switch { display: flex; gap: 0.5rem; align-self: start; }
    .medical-gender__switch button {
      cursor: pointer;
      border: 1px solid color-mix(in srgb, var(--border) 75%, transparent);
      background: color-mix(in srgb, var(--background) 95%, var(--muted));
      color: var(--foreground);
      padding: 0.45rem 0.9rem;
      font-size: 0.78rem;
      line-height: 1;
      font-weight: 600;
      border-radius: 0.65rem;
      transition: background 0.25s, color 0.25s, box-shadow 0.25s;
    }
    .medical-gender__switch button:is(:hover, :focus-visible) {
      background: color-mix(in srgb, var(--background) 88%, var(--accent));
      outline: 0;
    }
    .medical-gender__switch button.is-active {
      background: var(--accent);
      color: var(--background);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    .medical-gender__growth-toggle {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 2;
      cursor: pointer;
      border: 1px solid color-mix(in srgb, var(--border) 75%, transparent);
      background: color-mix(in srgb, var(--background) 95%, var(--muted));
      color: var(--foreground);
      padding: 0.45rem 0.9rem;
      font-size: 0.78rem;
      line-height: 1;
      font-weight: 600;
      border-radius: 0.65rem;
      box-shadow: 0 6px 14px rgba(15, 23, 42, 0.12);
      transition: background 0.25s, color 0.25s, box-shadow 0.25s, transform 0.2s ease;
    }
    .medical-gender__growth-toggle:is(:hover, :focus-visible) {
      background: color-mix(in srgb, var(--background) 88%, var(--accent));
      outline: 0;
      transform: translateY(-1px);
    }
    .medical-gender__growth-toggle.is-active {
      background: var(--accent);
      color: var(--background);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    .medical-gender__header { display: flex; flex-direction: column; gap: 0.75rem; }
    .medical-gender__titles { max-width: 60ch; }
    @media (min-width: 680px) {
      .medical-gender__header { flex-direction: row; justify-content: space-between; align-items: flex-start; }
      .medical-gender__titles { max-width: 60%; }
    }
    @media (max-width: 639px) {
      .medical-gender__card { padding: 1rem 1.05rem 1.25rem; gap: 0.65rem; }
      .medical-gender__header h3 { font-size: 1rem; }
      .medical-gender__header p { font-size: 0.82rem; }
      .medical-gender__switch button { padding: 0.4rem 0.7rem; font-size: 0.7rem; }
      .medical-gender__growth-toggle { top: 6px; right: 6px; }
      .medical-gender__plot { min-height: 300px; }
      .medical-gender__card figcaption { font-size: 0.78rem; }
    }
    .medical-gender__card figcaption {
      font-size: 0.92rem;
      color: color-mix(in srgb, var(--foreground) 72%, var(--muted));
      line-height: 1.5;
    }
  `;
  document.head.append(style);
};

const getCssVar = (variable: string) => {
  const computed = getComputedStyle(document.documentElement);
  const value = computed.getPropertyValue(variable).trim();
  return value || undefined;
};

const clampChannel = (value: number) => Math.min(255, Math.max(0, Math.round(value)));

const hexToRgb = (hex: string) => {
  const raw = hex.replace("#", "").trim();
  if (![3, 6].includes(raw.length)) return null;
  const normalized = raw.length === 3 ? raw.split("").map(char => char + char).join("") : raw;
  const intVal = Number.parseInt(normalized, 16);
  const r = (intVal >> 16) & 255;
  const g = (intVal >> 8) & 255;
  const b = intVal & 255;
  return { r, g, b };
};

const withAlpha = (hex: string, alpha: number) => {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  return `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${alpha})`;
};

const lighten = (hex: string, amount: number) => {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  const factor = amount / 100;
  return `#${[rgb.r, rgb.g, rgb.b]
    .map(channel => clampChannel(channel + (255 - channel) * factor)
      .toString(16)
      .padStart(2, "0"))
    .join("")}`;
};

const darken = (hex: string, amount: number) => {
  const rgb = hexToRgb(hex);
  if (!rgb) return hex;
  const factor = amount / 100;
  return `#${[rgb.r, rgb.g, rgb.b]
    .map(channel => clampChannel(channel * (1 - factor))
      .toString(16)
      .padStart(2, "0"))
    .join("")}`;
};

const colorMix = (hexA: string, hexB: string) => {
  const rgbA = hexToRgb(hexA);
  const rgbB = hexToRgb(hexB);
  if (!rgbA || !rgbB) return hexA;
  const mix = (channel: keyof typeof rgbA) =>
    clampChannel((rgbA[channel] + rgbB[channel]) / 2)
      .toString(16)
      .padStart(2, "0");
  return `#${mix("r")}${mix("g")}${mix("b")}`;
};

const derivePalette = () => {
  const accent = getCssVar("--accent") ?? "#005f99";
  const foreground = getCssVar("--foreground") ?? "#242424";
  const background = getCssVar("--background") ?? "#fefefe";
  const muted = getCssVar("--muted") ?? "#d8d8d8";
  const female = accent;
  const male = darken(accent, 20);
  return {
    accent,
    female,
    femaleSoft: withAlpha(female, 0.28),
    male,
    maleSoft: withAlpha(lighten(male, 45), 0.24),
    foreground,
    background,
    muted,
  };
};

type Palette = ReturnType<typeof derivePalette>;

const buildCountsOption = (palette: Palette, compact = false, showDerivative = false) => {
  const grid = showDerivative
    ? (compact
      ? { top: 60, bottom: 54, left: 50, right: 78 }
      : { top: 68, bottom: 68, left: 72, right: 104 })
    : (compact
      ? { top: 60, bottom: 54, left: 50, right: 40 }
      : { top: 68, bottom: 68, left: 72, right: 68 });

  const baseAxis = {
    type: "value" as const,
    name: "Médicos",
    nameGap: compact ? 36 : 44,
    nameTextStyle: { color: palette.foreground, fontWeight: 600, fontSize: compact ? 12 : 13 },
    axisLabel: {
      color: palette.foreground,
      formatter: (value: number) => Number(value).toLocaleString("pt-BR"),
      fontSize: compact ? 11 : 12,
    },
    axisTick: { show: false },
    splitLine: {
      lineStyle: {
        color: withAlpha(palette.muted, 0.4),
      },
    },
  };

  const growthColor = lighten(palette.female, 20);

  const growthAxis = {
    type: "value" as const,
    name: "Crescimento anual",
    position: "right" as const,
    nameGap: compact ? 36 : 46,
    nameTextStyle: { color: palette.foreground, fontWeight: 600, fontSize: compact ? 12 : 13 },
    axisLine: { lineStyle: { color: withAlpha(palette.muted, 0.45) } },
    axisTick: { show: false },
    axisLabel: {
      color: palette.foreground,
      formatter: (value: number) => Number(value).toLocaleString("pt-BR"),
      fontSize: compact ? 11 : 12,
    },
    splitLine: { show: false },
  };

  const tooltipFormatter = (input: unknown) => {
    if (!Array.isArray(input) || !input.length) return "";
    const axisValue = input[0]?.axisValue;
    const dataIndex = typeof input[0]?.dataIndex === "number"
      ? input[0].dataIndex
      : YEARS.indexOf(Number(axisValue));
    const year = typeof axisValue === "string" ? Number.parseInt(axisValue, 10) : Number(axisValue);
    const row = Number.isFinite(year) ? DATA_BY_YEAR.get(year) : undefined;
    if (!row) return "";
    const status = row.projection ? "Projeção" : "Observado";
    const growthValue = showDerivative && dataIndex > 0 ? TOTAL_DELTA[dataIndex] : null;
    const growthLine = Number.isFinite(growthValue ?? NaN)
      ? `<span>Variação anual: <strong>${(growthValue as number).toLocaleString("pt-BR")}</strong></span>`
      : "";
    return `
      <div style="display:flex;flex-direction:column;gap:0.2rem;min-width:180px;">
        <span style="font-weight:600;">${year} · ${status}</span>
        <span>Mulheres: <strong>${row.female.toLocaleString("pt-BR")}</strong> (${row.femaleShare.toFixed(1)}%)</span>
        <span>Homens: <strong>${row.male.toLocaleString("pt-BR")}</strong> (${row.maleShare.toFixed(1)}%)</span>
        <span>Total: <strong>${row.total.toLocaleString("pt-BR")}</strong></span>
        ${growthLine}
      </div>
    `;
  };

  const series: Record<string, unknown>[] = [
    {
      name: "Mulheres",
      type: "line" as const,
      smooth: true,
      showSymbol: true,
      symbolSize: compact ? 6 : 8,
      lineStyle: { width: compact ? 2.2 : 2.8, color: palette.female },
      itemStyle: { color: palette.female },
      areaStyle: {
        color: new graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: withAlpha(palette.female, 0.32) },
          { offset: 1, color: withAlpha(palette.female, 0.04) },
        ]),
      },
      emphasis: { focus: "series" },
      yAxisIndex: 0,
      data: FEMALE_COUNTS,
    },
    {
      name: "Homens",
      type: "line" as const,
      smooth: true,
      showSymbol: true,
      symbol: "rect",
      symbolSize: compact ? 6 : 8,
      lineStyle: { width: compact ? 2 : 2.4, color: palette.male },
      itemStyle: { color: palette.male },
      areaStyle: {
        color: new graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: withAlpha(palette.male, 0.28) },
          { offset: 1, color: withAlpha(palette.male, 0.04) },
        ]),
      },
      emphasis: { focus: "series" },
      yAxisIndex: 0,
      data: MALE_COUNTS,
    },
    {
      name: "Total",
      type: "line" as const,
      smooth: true,
      showSymbol: false,
      lineStyle: {
        width: compact ? 2 : 2.4,
        type: "dashed",
        color: colorMix(palette.female, palette.male),
      },
      itemStyle: { color: palette.foreground },
      z: 3,
      yAxisIndex: 0,
      markArea: {
        silent: true,
        itemStyle: {
          color: withAlpha(palette.muted, 0.16),
        },
        label: {
          show: true,
          color: palette.foreground,
          fontWeight: 600,
          position: "insideTop",
          formatter: "Projeção 2024-2035",
        },
        data: [
          [
            { name: "Projeção", xAxis: PROJECTION_START },
            { xAxis: PROJECTION_END },
          ],
        ],
      },
      data: TOTAL_COUNTS,
    },
  ];

  if (showDerivative) {
    series.push({
      name: "Delta anual",
      type: "line" as const,
      smooth: true,
      showSymbol: true,
      symbol: "triangle",
      symbolSize: compact ? 6 : 8,
      yAxisIndex: 1,
      lineStyle: {
        width: compact ? 2 : 2.4,
        color: growthColor,
      },
      itemStyle: {
        color: growthColor,
      },
      areaStyle: {
        color: new graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: withAlpha(growthColor, 0.25) },
          { offset: 1, color: withAlpha(growthColor, 0.02) },
        ]),
      },
      emphasis: { focus: "series" },
      data: TOTAL_DELTA,
    });
  }

  return {
    animationDuration: 1_200,
    animationEasing: "cubicOut" as const,
    backgroundColor: palette.background,
    textStyle: {
      color: palette.foreground,
      fontFamily: "Peridot, system-ui, sans-serif",
    },
    legend: {
      top: compact ? 6 : 10,
      icon: "roundRect",
      itemWidth: compact ? 12 : 14,
      itemHeight: compact ? 7 : 8,
      itemGap: compact ? 12 : 18,
      textStyle: {
        color: palette.foreground,
        fontSize: compact ? 11 : 12,
      },
    },
    tooltip: {
      trigger: "axis" as const,
      axisPointer: {
        type: "shadow" as const,
        shadowStyle: { color: withAlpha(palette.accent, 0.12) },
        lineStyle: { color: palette.accent },
      },
      backgroundColor: withAlpha(palette.background, 0.97),
      borderColor: withAlpha(palette.accent, 0.8),
      borderWidth: 1,
      textStyle: {
        color: palette.foreground,
        fontSize: compact ? 11 : 12,
      },
      formatter: tooltipFormatter,
    },
    grid,
    xAxis: {
      type: "category" as const,
      boundaryGap: false,
      axisLine: { lineStyle: { color: palette.muted } },
      axisTick: { show: false },
      axisLabel: { color: palette.foreground, fontWeight: 500, fontSize: compact ? 11 : 12 },
      data: YEARS,
    },
    yAxis: showDerivative ? [baseAxis, growthAxis] : [baseAxis],
    series,
  };
};

const buildShareOption = (palette: Palette, compact = false) => ({
  animationDuration: 1_100,
  animationEasing: "cubicInOut" as const,
  backgroundColor: palette.background,
  textStyle: {
    color: palette.foreground,
    fontFamily: "Peridot, system-ui, sans-serif",
  },
  legend: {
    top: compact ? 6 : 10,
    icon: "roundRect",
    itemWidth: compact ? 12 : 14,
    itemHeight: compact ? 7 : 8,
    itemGap: compact ? 12 : 18,
    textStyle: {
      color: palette.foreground,
      fontSize: compact ? 11 : 12,
    },
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    backgroundColor: withAlpha(palette.background, 0.97),
    borderColor: withAlpha(palette.accent, 0.8),
    borderWidth: 1,
    textStyle: { color: palette.foreground, fontSize: compact ? 11 : 12 },
    formatter: (input: unknown) => {
      if (!Array.isArray(input) || !input.length) return "";
      const axisValue = input[0]?.axisValue;
      const year = typeof axisValue === "string" ? Number.parseInt(axisValue, 10) : Number(axisValue);
      const row = Number.isFinite(year) ? DATA_BY_YEAR.get(year) : undefined;
      if (!row) return "";
      const status = row.projection ? "Projeção" : "Observado";
      return `
        <div style="display:flex;flex-direction:column;gap:0.2rem;min-width:150px;">
          <span style="font-weight:600;">${year} · ${status}</span>
          <span>Mulheres: <strong>${row.femaleShare.toFixed(1)}%</strong></span>
          <span>Homens: <strong>${row.maleShare.toFixed(1)}%</strong></span>
        </div>
      `;
    },
  },
  grid: compact
    ? { top: 60, bottom: 54, left: 50, right: 42 }
    : { top: 68, bottom: 68, left: 72, right: 68 },
  xAxis: {
    type: "category",
    boundaryGap: false,
    axisTick: { show: false },
    axisLine: { lineStyle: { color: palette.muted } },
    axisLabel: { color: palette.foreground, fontSize: compact ? 11 : 12 },
    data: YEARS,
  },
  yAxis: {
    type: "value",
    min: 40,
    max: 58,
    axisLine: { show: false },
    axisTick: { show: false },
    splitLine: { lineStyle: { color: withAlpha(palette.muted, 0.4) } },
    axisLabel: {
      color: palette.foreground,
      formatter: (value: number) => `${value.toFixed(0)}%`,
      fontSize: compact ? 11 : 12,
    },
  },
  series: [
    {
      name: "Mulheres",
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: compact ? 6 : 8,
      lineStyle: { width: compact ? 2.2 : 2.6, color: palette.female },
      itemStyle: { color: palette.female },
      areaStyle: {
        color: new graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: withAlpha(palette.female, 0.35) },
          { offset: 1, color: withAlpha(palette.female, 0.05) },
        ]),
      },
      emphasis: { focus: "series" },
      markArea: {
        silent: true,
        itemStyle: { color: withAlpha(palette.muted, 0.16) },
        label: {
          show: true,
          color: palette.foreground,
          fontWeight: 600,
          position: "insideTop",
          formatter: "Projeção 2024-2035",
        },
        data: [
          [
            { name: "Projeção", xAxis: PROJECTION_START },
            { xAxis: PROJECTION_END },
          ],
        ],
      },
      markLine: {
        symbol: "none",
        lineStyle: {
          type: "dashed",
          color: withAlpha(palette.foreground, 0.5),
          width: 1.5,
        },
        label: {
          formatter: "Paridade",
          color: palette.foreground,
          fontWeight: 600,
          backgroundColor: withAlpha(palette.background, 0.9),
          padding: [4, 6],
          borderRadius: 6,
        },
        data: [{ yAxis: 50 }],
      },
      data: FEMALE_SHARE,
    },
    {
      name: "Homens",
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: compact ? 6 : 8,
      lineStyle: { width: compact ? 2 : 2.4, color: palette.male },
      itemStyle: { color: palette.male },
      areaStyle: {
        color: new graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: withAlpha(palette.male, 0.32) },
          { offset: 1, color: withAlpha(palette.male, 0.05) },
        ]),
      },
      emphasis: { focus: "series" },
      data: MALE_SHARE,
    },
  ],
});

class MedicalGenderChartElement extends HTMLElement {
  private countsChart?: EChartsType;
  private shareChart?: EChartsType;
  private resizeObserver?: ResizeObserver;
  private themeObserver?: MutationObserver;
  private mounted = false;
  private active: "counts" | "share" = "counts";
  private compact = false;
  private showDerivative = false;
  private derivativeButton?: HTMLButtonElement;

  connectedCallback() {
    if (this.mounted || typeof window === "undefined") return;
    this.mounted = true;
    ensureStyles();
    this.innerHTML = FIGURE_TEMPLATE;

    const countsTarget = this.querySelector<HTMLElement>("[data-chart=counts]");
    const shareTarget = this.querySelector<HTMLElement>("[data-chart=share]");
    if (!countsTarget || !shareTarget) return;

    this.derivativeButton = this.querySelector<HTMLButtonElement>("[data-derivative-toggle]") || undefined;

    const syncDerivativeButton = () => {
      const btn = this.derivativeButton;
      if (!btn) return;
      btn.classList.toggle("is-active", this.showDerivative);
      btn.setAttribute("aria-pressed", String(this.showDerivative));
      btn.textContent = this.showDerivative ? "Ocultar crescimento" : "Ver crescimento";
      btn.toggleAttribute("hidden", this.active !== "counts");
      btn.tabIndex = this.active === "counts" ? 0 : -1;
    };

    const palette = derivePalette();

    this.countsChart = init(countsTarget, undefined, { renderer: "canvas" });
    this.shareChart = init(shareTarget, undefined, { renderer: "canvas" });

    const computeCompact = () => {
      const width = this.clientWidth || window.innerWidth;
      return width < 640;
    };
    this.compact = computeCompact();

    syncDerivativeButton();

    const loadingConfig = {
      text: "Carregando cenário...",
      color: palette.accent,
      textColor: palette.foreground,
      maskColor: withAlpha(palette.background, 0.92),
      zlevel: 0,
    };

    this.countsChart.showLoading("default", loadingConfig);
    this.shareChart.showLoading("default", loadingConfig);

    requestAnimationFrame(() => {
      try {
        this.countsChart?.setOption(buildCountsOption(palette, this.compact, this.showDerivative), { notMerge: true });
      } catch (err) {
        const g = globalThis as unknown as Record<string, unknown>;
        if (g.__MEDICAL_GENDER_DEBUG__) {
          g.__medicalGenderLastError = err as unknown;
        }
      } finally {
        this.countsChart?.hideLoading();
      }
      try {
        this.shareChart?.setOption(buildShareOption(palette, this.compact), { notMerge: true });
      } catch (err) {
        const g = globalThis as unknown as Record<string, unknown>;
        if (g.__MEDICAL_GENDER_DEBUG__) {
          g.__medicalGenderLastError = err as unknown;
        }
      } finally {
        this.shareChart?.hideLoading();
      }
      this.countsChart?.resize();
      this.shareChart?.resize();
    });

    this.resizeObserver = new ResizeObserver(entries => {
      if (!Array.isArray(entries)) return;
      const nextCompact = computeCompact();
      const changed = nextCompact !== this.compact;
      this.compact = nextCompact;
      window.requestAnimationFrame(() => {
        if (changed) {
          const nextPalette = derivePalette();
          this.countsChart?.setOption(buildCountsOption(nextPalette, this.compact, this.showDerivative), { notMerge: true });
          this.shareChart?.setOption(buildShareOption(nextPalette, this.compact), { notMerge: true });
        }
        this.countsChart?.resize();
        this.shareChart?.resize();
      });
    });
    this.resizeObserver.observe(this);

    this.themeObserver = new MutationObserver(mutations => {
      if (!mutations.some(({ attributeName }) => attributeName === "data-theme")) return;
      const nextPalette = derivePalette();
      const countsOption = buildCountsOption(nextPalette, this.compact, this.showDerivative);
      const shareOption = buildShareOption(nextPalette, this.compact);
      this.countsChart?.setOption(countsOption, { notMerge: true, lazyUpdate: true });
      this.shareChart?.setOption(shareOption, { notMerge: true, lazyUpdate: true });
      requestAnimationFrame(() => {
        this.countsChart?.resize();
        this.shareChart?.resize();
      });
    });
    this.themeObserver.observe(document.documentElement, { attributes: true });

    this.derivativeButton?.addEventListener("click", () => {
      this.showDerivative = !this.showDerivative;
      syncDerivativeButton();
      const nextPalette = derivePalette();
      this.countsChart?.setOption(buildCountsOption(nextPalette, this.compact, this.showDerivative), { notMerge: true });
      requestAnimationFrame(() => {
        this.countsChart?.resize();
      });
    });

    const switcher = this.querySelector(".medical-gender__switch");
    const countsPanel = this.querySelector<HTMLElement>("#gender-panel-counts");
    const sharePanel = this.querySelector<HTMLElement>("#gender-panel-share");
    const countsCaption = this.querySelector<HTMLElement>("p[data-caption=counts]");
    const shareCaption = this.querySelector<HTMLElement>("p[data-caption=share]");
    const titleEl = this.querySelector<HTMLElement>("#gender-chart-title");
    const subtitleEl = this.querySelector<HTMLElement>("#gender-chart-subtitle");

    const applyState = (next: "counts" | "share") => {
      if (this.active === next) {
        syncDerivativeButton();
        return;
      }
      this.active = next;
      const buttons = switcher?.querySelectorAll<HTMLButtonElement>("button[role=tab]");
      buttons?.forEach(btn => {
        const isActive = btn.dataset.switch === next;
        btn.classList.toggle("is-active", isActive);
        btn.setAttribute("aria-selected", String(isActive));
        btn.tabIndex = isActive ? 0 : -1;
      });

      if (next === "counts") {
        countsPanel?.classList.remove("is-hidden");
        countsPanel?.removeAttribute("hidden");
        sharePanel?.classList.add("is-hidden");
        sharePanel?.setAttribute("hidden", "");
        countsCaption?.removeAttribute("hidden");
        shareCaption?.setAttribute("hidden", "");
        if (titleEl) titleEl.textContent = "Mulheres puxam o crescimento da categoria";
        if (subtitleEl) subtitleEl.textContent = "Número de médicos por sexo — Brasil (2009-2035)";
        this.countsChart?.resize();
      } else {
        sharePanel?.classList.remove("is-hidden");
        sharePanel?.removeAttribute("hidden");
        countsPanel?.classList.add("is-hidden");
        countsPanel?.setAttribute("hidden", "");
        shareCaption?.removeAttribute("hidden");
        countsCaption?.setAttribute("hidden", "");
        if (titleEl) titleEl.textContent = "Quando as médicas se tornam maioria?";
        if (subtitleEl) subtitleEl.textContent = "Participação percentual de médicas e médicos — Brasil";
        this.shareChart?.resize();
      }
      syncDerivativeButton();
    };

    switcher?.addEventListener("click", event => {
      const target = event.target as HTMLElement | null;
      if (!target || target.getAttribute("role") !== "tab") return;
      const key = (target as HTMLButtonElement).dataset.switch;
      if (key === "counts" || key === "share") applyState(key);
    });

    switcher?.addEventListener("keydown", event => {
      const keyboard = event as KeyboardEvent;
      const order: Array<"counts" | "share"> = ["counts", "share"];
      if (keyboard.key === "ArrowRight" || keyboard.key === "ArrowLeft") {
        keyboard.preventDefault();
        const idx = order.indexOf(this.active);
        const nextIdx = keyboard.key === "ArrowRight"
          ? (idx + 1) % order.length
          : (idx - 1 + order.length) % order.length;
        const next = order[nextIdx];
        applyState(next);
        const btn = switcher?.querySelector<HTMLButtonElement>(`button[data-switch=${next}]`);
        btn?.focus();
      }
    });
  }

  disconnectedCallback() {
    this.resizeObserver?.disconnect();
    this.themeObserver?.disconnect();
    this.resizeObserver = undefined;
    this.themeObserver = undefined;

    if (this.countsChart) {
      this.countsChart.dispose();
      this.countsChart = undefined;
    }
    if (this.shareChart) {
      this.shareChart.dispose();
      this.shareChart = undefined;
    }
    this.mounted = false;
    this.derivativeButton = undefined;
    this.showDerivative = false;
  }
}

if (!customElements.get("medical-gender-chart")) {
  customElements.define("medical-gender-chart", MedicalGenderChartElement);
}
