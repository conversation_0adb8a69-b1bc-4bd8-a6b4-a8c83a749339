---
// Newsletter component for email subscriptions
import IconMail from "@/assets/icons/IconMail.svg";
---

<div class="newsletter-container bg-gradient-to-r from-accent/5 to-accent/10 rounded-xl p-8 my-12 border border-accent/20">
  <div class="text-center max-w-2xl mx-auto">
    <div class="mb-4">
      <div class="flex justify-center mb-2">
        <IconMail
          width={32}
          height={32}
          class="stroke-current text-accent"
        />
      </div>
      <h3 class="text-2xl font-bold mb-3 text-accent">
        Fique por dentro das novidades!
      </h3>
    </div>
    <p class="text-foreground/80 mb-8 text-lg leading-relaxed">
      Receba atualizações sobre novos artigos, reflexões sobre saúde pública e tecnologias em saúde diretamente no seu email.
    </p>
    
    <form id="newsletter-form" class="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto mb-6">
      <div class="flex-1 relative">
        <input
          type="email"
          id="newsletter-email"
          name="email"
          placeholder="Seu melhor email"
          required
          class="w-full px-5 py-4 rounded-xl border-2 border-border bg-background text-foreground placeholder:text-foreground/50 focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent transition-all duration-200 text-base"
        />
        <div id="email-validation" class="absolute -bottom-6 left-0 text-sm text-red-500 hidden"></div>
      </div>
      <button
        type="submit"
        id="newsletter-submit"
        class="px-8 py-4 bg-accent text-background font-semibold rounded-xl hover:bg-accent/90 active:scale-95 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 min-w-[140px] flex items-center justify-center gap-2"
      >
        <span id="submit-text">Inscrever-se</span>
        <div id="loading-spinner" class="hidden w-4 h-4 border-2 border-background/30 border-t-background rounded-full animate-spin"></div>
      </button>
    </form>
    
    <div id="newsletter-message" class="mb-6 text-base font-medium hidden"></div>
  
  </div>
</div>

<script>
  import { ConvexHttpClient } from 'convex/browser';

  // Initialize Convex client
  const CONVEX_URL = import.meta.env.PUBLIC_CONVEX_URL || '';
  const convex = new ConvexHttpClient(CONVEX_URL);

  document.addEventListener('astro:page-load', () => {
    const form = document.getElementById('newsletter-form') as HTMLFormElement;
    const emailInput = document.getElementById('newsletter-email') as HTMLInputElement;
    const messageDiv = document.getElementById('newsletter-message') as HTMLDivElement;
    const submitButton = document.getElementById('newsletter-submit') as HTMLButtonElement;
    const submitText = document.getElementById('submit-text') as HTMLSpanElement;
    const loadingSpinner = document.getElementById('loading-spinner') as HTMLDivElement;
    const emailValidation = document.getElementById('email-validation') as HTMLDivElement;

    if (!form || !emailInput || !messageDiv || !submitButton || !submitText || !loadingSpinner || !emailValidation) return;

    // Real-time email validation
    emailInput.addEventListener('input', () => {
      const email = emailInput.value.trim();
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      
      if (email && !emailRegex.test(email)) {
        emailValidation.textContent = 'Por favor, insira um email válido';
        emailValidation.classList.remove('hidden');
        emailInput.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
        emailInput.classList.remove('focus:border-accent', 'focus:ring-accent');
      } else {
        emailValidation.classList.add('hidden');
        emailInput.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
        emailInput.classList.add('focus:border-accent', 'focus:ring-accent');
      }
    });

    const showMessage = (message: string, isSuccess: boolean) => {
      messageDiv.textContent = message;
      messageDiv.className = `mb-6 text-base font-medium p-4 rounded-lg ${
        isSuccess 
          ? 'text-green-700 bg-green-50 border border-green-200' 
          : 'text-red-700 bg-red-50 border border-red-200'
      }`;
      messageDiv.classList.remove('hidden');
      
      // Auto-hide after 8 seconds
      setTimeout(() => {
        messageDiv.classList.add('hidden');
      }, 8000);
    };

    const setLoading = (loading: boolean) => {
      if (loading) {
        submitButton.disabled = true;
        submitButton.classList.add('opacity-80', 'cursor-not-allowed');
        submitText.classList.add('hidden');
        loadingSpinner.classList.remove('hidden');
      } else {
        submitButton.disabled = false;
        submitButton.classList.remove('opacity-80', 'cursor-not-allowed');
        submitText.classList.remove('hidden');
        loadingSpinner.classList.add('hidden');
      }
    };

    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const email = emailInput.value.trim();
      
      if (!email) {
        showMessage('Por favor, insira seu email.', false);
        emailInput.focus();
        return;
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        showMessage('Por favor, insira um email válido.', false);
        emailInput.focus();
        return;
      }

      setLoading(true);

      try {
        if (!CONVEX_URL) {
          throw new Error('Convex URL not configured');
        }

        // Call Convex action directly
        // @ts-expect-error - Action reference will be available after convex dev
        const result = await convex.action('newsletter:subscribeWithVerification', { 
          email: email.toLowerCase() 
        });

        if (result.success) {
          // Handle different success codes
          if (result.code === 'VERIFICATION_SENT' || result.code === 'VERIFICATION_RESENT') {
            showMessage('✉️ ' + result.message + ' Verifique também sua pasta de spam.', true);
          } else {
            showMessage('🎉 ' + result.message, true);
          }
          emailInput.value = '';
          
          // Celebrate with a subtle animation
          form.classList.add('animate-pulse');
          setTimeout(() => {
            form.classList.remove('animate-pulse');
          }, 1000);
        } else {
          // Handle different error codes
          if (result.code === 'ALREADY_VERIFIED') {
            showMessage('✅ ' + result.message, false);
          } else {
            showMessage(result.message || 'Erro ao realizar inscrição. Tente novamente.', false);
          }
        }
      } catch {
        showMessage('Erro de conexão. Verifique sua internet e tente novamente.', false);
      } finally {
        setLoading(false);
      }
    });

    // Add enter key support for better accessibility
    emailInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        form.dispatchEvent(new Event('submit'));
      }
    });
  });
</script> 