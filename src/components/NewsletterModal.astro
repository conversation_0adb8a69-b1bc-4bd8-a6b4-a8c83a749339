---
// Modal Newsletter component for email subscriptions
import IconMail from "@/assets/icons/IconMail.svg";
---

<!-- Modal Overlay -->
<div 
  id="newsletter-modal" 
  class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 opacity-0 invisible transition-all duration-300"
  role="dialog"
  aria-labelledby="newsletter-modal-title"
  aria-modal="true"
>
  <div 
    class="bg-background rounded-2xl shadow-2xl max-w-lg w-full relative transform scale-95 transition-transform duration-300"
    id="newsletter-modal-content"
  >
    <!-- Close Button -->
    <button
      id="newsletter-modal-close"
      class="absolute top-4 right-4 w-8 h-8 rounded-full bg-muted hover:bg-muted/80 flex items-center justify-center transition-colors z-10"
      aria-label="Fechar modal"
    >
      <span class="text-lg">×</span>
    </button>

    <!-- Modal Content -->
    <div class="p-8">
      <div class="text-center mb-6">
        <div class="flex justify-center mb-4">
          <IconMail
            width={48}
            height={48}
            class="stroke-current text-accent"
          />
        </div>
        <h2 id="newsletter-modal-title" class="text-2xl font-bold text-accent mb-3">
          Fique por dentro das novidades!
        </h2>
        <p class="text-foreground/80 text-base leading-relaxed">
          Receba atualizações sobre novos artigos, reflexões sobre saúde pública e tecnologias em saúde diretamente no seu email.
        </p>
      </div>
      
      <form id="newsletter-modal-form" class="space-y-4 mb-6">
        <div class="relative">
          <input
            type="email"
            id="newsletter-modal-email"
            name="email"
            placeholder="Seu melhor email"
            required
            class="w-full px-5 py-4 rounded-xl border-2 border-border bg-background text-foreground placeholder:text-foreground/50 focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent transition-all duration-200 text-base"
          />
          <div id="newsletter-modal-email-validation" class="absolute -bottom-6 left-0 text-sm text-red-500 hidden"></div>
        </div>
        
        <button
          type="submit"
          id="newsletter-modal-submit"
          class="w-full px-8 py-4 bg-accent text-background font-semibold rounded-xl hover:bg-accent/90 active:scale-95 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 flex items-center justify-center gap-2"
        >
          <span id="newsletter-modal-submit-text">Inscrever-se</span>
          <div id="newsletter-modal-loading-spinner" class="hidden w-4 h-4 border-2 border-background/30 border-t-background rounded-full animate-spin"></div>
        </button>
      </form>
      
      <div id="newsletter-modal-message" class="text-base font-medium hidden mb-4"></div>
      
      
    </div>
  </div>
</div>

<script>
  import { ConvexHttpClient } from 'convex/browser';

  // Initialize Convex client
  const CONVEX_URL = import.meta.env.PUBLIC_CONVEX_URL || '';
  const convex = new ConvexHttpClient(CONVEX_URL);

  document.addEventListener('astro:page-load', () => {
    const modal = document.getElementById('newsletter-modal');
    const modalContent = document.getElementById('newsletter-modal-content');
    const closeButton = document.getElementById('newsletter-modal-close');
    const form = document.getElementById('newsletter-modal-form') as HTMLFormElement;
    const emailInput = document.getElementById('newsletter-modal-email') as HTMLInputElement;
    const messageDiv = document.getElementById('newsletter-modal-message') as HTMLDivElement;
    const submitButton = document.getElementById('newsletter-modal-submit') as HTMLButtonElement;
    const submitText = document.getElementById('newsletter-modal-submit-text') as HTMLSpanElement;
    const loadingSpinner = document.getElementById('newsletter-modal-loading-spinner') as HTMLDivElement;
    const emailValidation = document.getElementById('newsletter-modal-email-validation') as HTMLDivElement;

    if (!modal || !modalContent || !closeButton || !form || !emailInput || !messageDiv || !submitButton || !submitText || !loadingSpinner || !emailValidation) return;

    // Open modal function - store on global object for external access
    const openModal = () => {
      modal.classList.remove('opacity-0', 'invisible');
      modalContent.classList.remove('scale-95');
      modalContent.classList.add('scale-100');
      document.body.classList.add('overflow-hidden');
      
      // Focus on email input after animation
      setTimeout(() => {
        emailInput.focus();
      }, 300);
    };
    
    // Make it globally accessible
    Object.assign(window, { openNewsletterModal: openModal });

    // Close modal function
    const closeModal = () => {
      modal.classList.add('opacity-0', 'invisible');
      modalContent.classList.remove('scale-100');
      modalContent.classList.add('scale-95');
      document.body.classList.remove('overflow-hidden');
      
      // Reset form
      form.reset();
      messageDiv.classList.add('hidden');
      emailValidation.classList.add('hidden');
      emailInput.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
      emailInput.classList.add('focus:border-accent', 'focus:ring-accent');
    };

    // Event listeners
    closeButton.addEventListener('click', closeModal);
    
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal();
      }
    });

    // Escape key to close
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !modal.classList.contains('invisible')) {
        closeModal();
      }
    });

    // Real-time email validation
    emailInput.addEventListener('input', () => {
      const email = emailInput.value.trim();
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      
      if (email && !emailRegex.test(email)) {
        emailValidation.textContent = 'Por favor, insira um email válido';
        emailValidation.classList.remove('hidden');
        emailInput.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
        emailInput.classList.remove('focus:border-accent', 'focus:ring-accent');
      } else {
        emailValidation.classList.add('hidden');
        emailInput.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
        emailInput.classList.add('focus:border-accent', 'focus:ring-accent');
      }
    });

    const showMessage = (message: string, isSuccess: boolean) => {
      messageDiv.textContent = message;
      messageDiv.className = `text-base font-medium p-4 rounded-lg mb-4 ${
        isSuccess 
          ? 'text-green-700 bg-green-50 border border-green-200' 
          : 'text-red-700 bg-red-50 border border-red-200'
      }`;
      messageDiv.classList.remove('hidden');
      
      // Auto-hide after 8 seconds
      setTimeout(() => {
        messageDiv.classList.add('hidden');
      }, 8000);
    };

    const setLoading = (loading: boolean) => {
      if (loading) {
        submitButton.disabled = true;
        submitButton.classList.add('opacity-80', 'cursor-not-allowed');
        submitText.classList.add('hidden');
        loadingSpinner.classList.remove('hidden');
      } else {
        submitButton.disabled = false;
        submitButton.classList.remove('opacity-80', 'cursor-not-allowed');
        submitText.classList.remove('hidden');
        loadingSpinner.classList.add('hidden');
      }
    };

    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const email = emailInput.value.trim();
      
      if (!email) {
        showMessage('Por favor, insira seu email.', false);
        emailInput.focus();
        return;
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        showMessage('Por favor, insira um email válido.', false);
        emailInput.focus();
        return;
      }

      setLoading(true);

      try {
        if (!CONVEX_URL) {
          throw new Error('Convex URL not configured');
        }

        // Call Convex action directly
        // @ts-expect-error - Action reference will be available after convex dev
        const result = await convex.action('newsletter:subscribeWithVerification', { 
          email: email.toLowerCase() 
        });

        if (result.success) {
          // Handle different success codes
          if (result.code === 'VERIFICATION_SENT' || result.code === 'VERIFICATION_RESENT') {
            showMessage('✉️ ' + result.message + ' Verifique também sua pasta de spam.', true);
          } else {
            showMessage('🎉 ' + result.message, true);
          }
          emailInput.value = '';
          
          // Celebrate with a subtle animation
          form.classList.add('animate-pulse');
          setTimeout(() => {
            form.classList.remove('animate-pulse');
          }, 1000);

          // Close modal after successful subscription
          setTimeout(() => {
            closeModal();
          }, 3000);
        } else {
          // Handle different error codes
          if (result.code === 'ALREADY_VERIFIED') {
            showMessage('✅ ' + result.message, false);
          } else {
            showMessage(result.message || 'Erro ao realizar inscrição. Tente novamente.', false);
          }
        }
      } catch {
        showMessage('Erro de conexão. Verifique sua internet e tente novamente.', false);
      } finally {
        setLoading(false);
      }
    });

    // Add enter key support for better accessibility
    emailInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        form.dispatchEvent(new Event('submit'));
      }
    });
  });
</script> 