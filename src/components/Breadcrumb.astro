---
// Remove current url path and remove trailing slash if exists
const currentUrlPath = Astro.url.pathname.replace(/\/+$/, "");

// Get url array from path
// eg: /tags/tailwindcss => ['tags', 'tailwindcss']
const breadcrumbList = currentUrlPath.split("/").slice(1);

// if breadcrumb is Home > Posts > 1 <etc>
// replace Posts with Posts (page number)
if (breadcrumbList[0] === "posts") {
  breadcrumbList.splice(0, 2, `Postagens (página ${breadcrumbList[1] || 1})`);
}

// if breadcrumb is Home > Tags > [tag] > [page] <etc>
// replace [tag] > [page] with [tag] (page number)
if (breadcrumbList[0] === "tags" && !isNaN(Number(breadcrumbList[2]))) {
  breadcrumbList.splice(
    1,
    3,
    `${breadcrumbList[1]} ${Number(breadcrumbList[2]) === 1 ? "" : "(page " + breadcrumbList[2] + ")"}`
  );
}
---

<nav class="mx-auto mt-8 mb-1 w-full max-w-app px-4" aria-label="breadcrumb">
  <ul
    class="font-light [&>li]:inline [&>li:not(:last-child)>a]:hover:opacity-100"
  >
    <li>
      <a href="/" class="opacity-80">Início</a>
      <span aria-hidden="true" class="opacity-80">&raquo;</span>
    </li>
    {
      breadcrumbList.map((breadcrumb, index) =>
        index + 1 === breadcrumbList.length ? (
          <li>
            <span
              class:list={["capitalize opacity-75", { lowercase: index > 0 }]}
              aria-current="page"
            >
              {/* make the last part lowercase in Home > Tags > some-tag */}
              {decodeURIComponent(breadcrumb)}
            </span>
          </li>
        ) : (
          <li>
            <a href={`/${breadcrumb}/`} class="capitalize opacity-70">
              {breadcrumb}
            </a>
            <span aria-hidden="true">&raquo;</span>
          </li>
        )
      )
    }
  </ul>
</nav>
