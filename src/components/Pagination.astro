---
import type { Page } from "astro";
import type { CollectionEntry } from "astro:content";
import IconArrowLeft from "@/assets/icons/IconArrowLeft.svg";
import IconArrowRight from "@/assets/icons/IconArrowRight.svg";
import LinkButton from "./LinkButton.astro";

export interface Props {
  page: Page<CollectionEntry<"blog">>;
}

const { page } = Astro.props;
---

{
  page.lastPage > 1 && (
    <nav class="mt-auto mb-8 flex justify-center" aria-label="Pagination">
      <LinkButton
        disabled={!page.url.prev}
        href={page.url.prev as string}
        class:list={["me-4 select-none", { "opacity-50": !page.url.prev }]}
        ariaLabel="Previous"
      >
        <IconArrowLeft class="inline-block rtl:rotate-180" />
        Prev
      </LinkButton>
      {page.currentPage} / {page.lastPage}
      <LinkButton
        disabled={!page.url.next}
        href={page.url.next as string}
        class:list={["ms-4 select-none", { "opacity-50": !page.url.next }]}
        ariaLabel="Next"
      >
        Next
        <IconArrowRight class="inline-block rtl:rotate-180" />
      </LinkButton>
    </nav>
  )
}
