---
import { S<PERSON><PERSON><PERSON> } from "@/constants";
import LinkButton from "./LinkButton.astro";

export interface Props {
  centered?: boolean;
}

const { centered = false } = Astro.props;
---

<div class:list={["flex-wrap justify-center gap-1", { flex: centered }]}>
  {
    SOCIALS.map(social => (
      <LinkButton
        href={social.href}
        class="p-2 hover:rotate-6 sm:p-1"
        title={social.linkTitle}
      >
        <social.icon class="inline-block size-6 scale-125 fill-transparent stroke-current stroke-2 opacity-90 group-hover:fill-transparent sm:scale-110" />
        <span class="sr-only">{social.linkTitle}</span>
      </LinkButton>
    ))
  }
</div>
