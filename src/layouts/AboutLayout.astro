---
import Header from "@/components/Header.astro";
import Footer from "@/components/Footer.astro";
import Breadcrumb from "@/components/Breadcrumb.astro";
import Layout from "./Layout.astro";
import { SITE } from "@/config";

export interface Props {
  frontmatter: {
    title: string;
    description?: string;
  };
}

const { frontmatter } = Astro.props;
---

<Layout title={`${frontmatter.title} | ${SITE.title}`}>
  <Header />
  <Breadcrumb />
  <main id="main-content">
    <section id="about" class="app-prose mb-28 max-w-app prose-img:border-0">
      <h1 class="text-2xl tracking-wider sm:text-3xl">{frontmatter.title}</h1>
      <slot />
    </section>
  </main>
  <Footer />
</Layout>

<style>
  .author-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin: 2rem 0;
  }

  .author-photo {
    display: flex;
    justify-content: center;
    position: relative;
  }

  .author-photo::before {
    content: '';
    position: absolute;
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
    background: linear-gradient(45deg, #3b82f6, #06b6d4, #10b981, #f59e0b, #ef4444, #8b5cf6);
    border-radius: 50%;
    z-index: -1;
    opacity: 0.8;
    animation: rotate 3s linear infinite;
  }

  .author-photo img {
    width: 12rem;
    height: 12rem;
    object-fit: cover;
    border-radius: 50%;
    position: relative;
    z-index: 1;
    background: white;
    border: 4px solid white;
  }

  .author-content {
    text-align: center;
  }

  .author-content p {
    margin-bottom: 1rem;
    font-size: 1rem;
    line-height: 1.625;
  }

  /* Desktop styles */
  @media (min-width: 768px) {
    .author-section {
      flex-direction: row;
      align-items: center;
      gap: 2rem;
    }

    .author-photo {
      flex-shrink: 0;
      justify-content: flex-start;
    }

    .author-photo img {
      width: 14rem;
      height: 14rem;
    }

    .author-content {
      text-align: left;
      flex: 1;
    }
  }

  @keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Dark mode support */
  html[data-theme="dark"] .author-photo img {
    background: var(--color-fill);
    border-color: var(--color-fill);
  }

  /* Additional styling for better integration */
  .author-section {
    padding: 1.5rem;
    border-radius: 0.75rem;
    background: rgba(var(--color-accent), 0.05);
    border: 1px solid rgba(var(--color-accent), 0.1);
  }
</style>
