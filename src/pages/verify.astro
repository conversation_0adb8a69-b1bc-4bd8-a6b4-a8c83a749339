---
import Layout from "@/layouts/Layout.astro";
import Header from "@/components/Header.astro";
import Footer from "@/components/Footer.astro";
import LinkButton from "@/components/LinkButton.astro";
import { SITE } from "@/config";

const layoutProps = {
  title: `Verificação de Email | ${SITE.title}`,
  description: "Confirme sua inscrição na newsletter do Saúde com Alex",
};
---

<Layout {...layoutProps}>
  <Header />
  <main id="main-content" class="mx-auto w-full max-w-app px-4 py-12">
    <div class="mx-auto max-w-2xl text-center">
      
      <!-- Loading State -->
      <div id="loading-state" class="space-y-8">
        <div class="text-6xl mb-4">⏳</div>
        <div>
          <h1 class="text-3xl font-bold text-accent mb-4">
            Verificando seu email...
          </h1>
          <p class="text-lg text-foreground/80 mb-6">
            Aguarde enquanto confirmamos sua inscrição.
          </p>
        </div>
        <div class="flex justify-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-accent"></div>
        </div>
      </div>

      <!-- Success State -->
      <div id="success-state" class="space-y-8 hidden" data-success="true">
        <div class="text-6xl mb-4">🎉</div>
        <div>
          <h1 class="text-3xl font-bold text-accent mb-4">
            Email Verificado com Sucesso!
          </h1>
          <p class="text-lg text-foreground/80 mb-6">
            Obrigado por confirmar sua inscrição na newsletter <strong>Saúde com Alex</strong>!
          </p>
        </div>
        
        <div class="bg-green-50 border border-green-200 rounded-xl p-6 text-green-800">
          <div class="flex items-center justify-center mb-4">
            <span class="text-2xl">✅</span>
          </div>
          <h3 class="font-semibold mb-2">Inscrição Confirmada</h3>
          <p class="text-sm">
            Você agora receberá nossos conteúdos exclusivos sobre saúde pública, 
            medicina e tecnologias em saúde diretamente no seu email.
          </p>
        </div>
        
        <div class="space-y-4">
          <h3 class="text-xl font-semibold text-accent">O que você receberá:</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
            <div class="bg-muted/30 rounded-lg p-4">
              <div class="text-2xl mb-2">📚</div>
              <h4 class="font-medium mb-1">Artigos Exclusivos</h4>
              <p class="text-sm text-foreground/70">Reflexões e insights sobre saúde pública</p>
            </div>
            <div class="bg-muted/30 rounded-lg p-4">
              <div class="text-2xl mb-2">🩺</div>
              <h4 class="font-medium mb-1">Medicina Atual</h4>
              <p class="text-sm text-foreground/70">Novidades em educação médica e SUS</p>
            </div>
            <div class="bg-muted/30 rounded-lg p-4">
              <div class="text-2xl mb-2">💡</div>
              <h4 class="font-medium mb-1">Tecnologia em Saúde</h4>
              <p class="text-sm text-foreground/70">Inovações e metodologias ativas</p>
            </div>
            <div class="bg-muted/30 rounded-lg p-4">
              <div class="text-2xl mb-2">🎯</div>
              <h4 class="font-medium mb-1">Conteúdo Direcionado</h4>
              <p class="text-sm text-foreground/70">Informações práticas e aplicáveis</p>
            </div>
          </div>
        </div>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <LinkButton 
            href="/posts/" 
            class="bg-accent text-background hover:bg-accent/90 hover:text-background px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Explorar Artigos
          </LinkButton>
          <LinkButton 
            href="/" 
            class="border border-border hover:bg-muted px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Voltar ao Início
          </LinkButton>
        </div>
      </div>

      <!-- Error State -->
      <div id="error-state" class="space-y-8 hidden">
        <div class="text-6xl mb-4" id="error-icon">❌</div>
        <div>
          <h1 class="text-3xl font-bold text-red-600 mb-4" id="error-title">
            Verificação Falhou
          </h1>
          <p class="text-lg text-foreground/80 mb-6" id="error-message">
            Houve um problema ao verificar seu email.
          </p>
        </div>
        
        <div class="bg-red-50 border border-red-200 rounded-xl p-6 text-red-800">
          <div class="flex items-center justify-center mb-4">
            <span class="text-2xl">⚠️</span>
          </div>
          <h3 class="font-semibold mb-2" id="error-help-title">
            Problema na Verificação
          </h3>
          <p class="text-sm" id="error-help-text">
            O link de verificação pode estar incorreto ou já ter sido usado. Tente se inscrever novamente.
          </p>
        </div>
        
        <div class="space-y-4">
          <h3 class="text-xl font-semibold text-accent">Como resolver:</h3>
          <div class="text-left space-y-3">
            <div class="flex items-start gap-3">
              <span class="text-accent font-bold">1.</span>
              <p>Volte à página inicial e inscreva-se novamente na newsletter</p>
            </div>
            <div class="flex items-start gap-3">
              <span class="text-accent font-bold">2.</span>
              <p>Verifique sua caixa de entrada (e spam) pelo novo email</p>
            </div>
            <div class="flex items-start gap-3">
              <span class="text-accent font-bold">3.</span>
              <p>Clique no link de verificação dentro de 24 horas</p>
            </div>
          </div>
        </div>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <LinkButton 
            href="/" 
            class="bg-accent text-background hover:bg-accent/90 hover:text-background px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Tentar Novamente
          </LinkButton>
          <LinkButton 
            href="/posts/" 
            class="border border-border hover:bg-muted px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Explorar Artigos
          </LinkButton>
        </div>
      </div>
      
      <div class="mt-12 pt-8 border-t border-border">
        <p class="text-sm text-foreground/60">
          Precisa de ajuda? Entre em contato através das redes sociais disponíveis no rodapé da página.
        </p>
      </div>
    </div>
  </main>
  <Footer />
</Layout>

<script>
  import { ConvexHttpClient } from 'convex/browser';

  document.addEventListener('astro:page-load', async () => {
    // Get token from URL query parameter (e.g., /verify?token=abc123)
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');

    if (!token) {
      window.location.href = '/404';
      return;
    }

    // Initialize Convex client
    const CONVEX_URL = import.meta.env.PUBLIC_CONVEX_URL || '';
    
    // Get DOM elements
    const loadingState = document.getElementById('loading-state');
    const successState = document.getElementById('success-state');
    const errorState = document.getElementById('error-state');
    const errorIcon = document.getElementById('error-icon');
    const errorTitle = document.getElementById('error-title');
    const errorMessage = document.getElementById('error-message');
    const errorHelpTitle = document.getElementById('error-help-title');
    const errorHelpText = document.getElementById('error-help-text');

    if (!loadingState || !successState || !errorState) return;

    const showSuccess = () => {
      loadingState.classList.add('hidden');
      successState.classList.remove('hidden');
      errorState.classList.add('hidden');
      
      // Add celebration animation
      successState.style.animation = 'bounce 1s ease-in-out';
    };

    const showError = (isExpired = false, message = '') => {
      loadingState.classList.add('hidden');
      successState.classList.add('hidden');
      errorState.classList.remove('hidden');

      if (isExpired && errorIcon && errorTitle && errorMessage && errorHelpTitle && errorHelpText) {
        errorIcon.textContent = '⏰';
        errorTitle.textContent = 'Link Expirado';
        errorMessage.textContent = message || 'Token de verificação expirado.';
        errorHelpTitle.textContent = 'O que fazer agora?';
        errorHelpText.textContent = 'Não se preocupe! Você pode se inscrever novamente na nossa newsletter para receber um novo email de verificação.';
      } else if (errorMessage) {
        errorMessage.textContent = message || 'Houve um problema ao verificar seu email.';
      }
    };

    try {
      if (!CONVEX_URL) {
        // Fallback simulation for when Convex is not configured
        await simulateVerification(token);
        return;
      }

      const convex = new ConvexHttpClient(CONVEX_URL);

      // First, get subscriber info
      // @ts-expect-error - Query reference will be available after convex dev
      const subscriberInfo = await convex.query('newsletter:getSubscriberByToken', { token });
      
      if (!subscriberInfo) {
        showError(false, 'Token de verificação inválido.');
        return;
      }

      if (subscriberInfo.isExpired) {
        showError(true, 'Token de verificação expirado. Solicite um novo email de verificação.');
        return;
      }

      // Token is valid, now verify it
      // @ts-expect-error - Mutation reference will be available after convex dev
      const result = await convex.mutation('newsletter:verifyEmail', { token });
      
      if (result.success) {
        showSuccess();
      } else {
        showError(result.code === 'TOKEN_EXPIRED', result.message);
      }

    } catch {
      // Fallback to simulation if Convex fails
      await simulateVerification(token);
    }

    // Fallback simulation function
    async function simulateVerification(token: string) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate different scenarios based on token
      if (token === 'expired') {
        showError(true, 'Token de verificação expirado. Solicite um novo email de verificação.');
        return;
      }
      
      if (token === 'invalid') {
        showError(false, 'Token de verificação inválido.');
        return;
      }
      
      // For demo purposes, most tokens are valid
      if (token.length >= 8) {
        showSuccess();
        return;
      }
      
      showError(false, 'Token de verificação inválido.');
    }
  });
</script>

<style>
  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }
</style> 