---
import { getCollection } from "astro:content";
import Layout from "@/layouts/Layout.astro";
import Header from "@/components/Header.astro";
import Footer from "@/components/Footer.astro";
import Socials from "@/components/Socials.astro";
import LinkButton from "@/components/LinkButton.astro";
import Card from "@/components/Card.astro";
import Hr from "@/components/Hr.astro";
import NewsletterModal from "@/components/NewsletterModal.astro";
import getSortedPosts from "@/utils/getSortedPosts";
import IconRss from "@/assets/icons/IconRss.svg";
import IconArrowRight from "@/assets/icons/IconArrowRight.svg";
import IconMail from "@/assets/icons/IconMail.svg";
import { SITE } from "@/config";
import { SOCIALS } from "@/constants";

const posts = await getCollection("blog");

const sortedPosts = getSortedPosts(posts);
const featuredPosts = sortedPosts.filter(({ data }) => data.featured);
const recentPosts = sortedPosts.filter(({ data }) => !data.featured);
---

<Layout>
  <Header />
  <main id="main-content" data-layout="index">
    <section id="hero" class="pt-8 pb-12">
      <div class="text-left sm:text-left">
        <h1 class="my-4 inline-block text-4xl font-bold text-accent sm:my-8 sm:text-4xl">
          Bem-vindo/a/e!
        </h1>
      </div>

      <div class="max-w-3xl">
        <p class="text-lg text-foreground/80 leading-relaxed mb-4">
          Olá! Eu sou o <strong class="text-accent">Alexandre Borges Filho</strong>, estudante de medicina e apaixonado por saúde pública. 
          Aqui compartilho experiências, pesquisas e reflexões sobre medicina, tecnologias em saúde e educação médica.
        </p>
        <p class="text-base text-foreground/70 mb-6">
          Explore artigos sobre o SUS, metodologias ativas de ensino, gamificação em saúde 
          e muito mais. Acesse os links abaixo para saber mais sobre mim e o meu trabalho.
        </p>
      </div>

      <div class="flex flex-wrap items-center gap-4 mb-6">
        <LinkButton 
          href="/posts/" 
          class="bg-accent text-background hover:bg-accent/90 hover:text-background px-6 py-3 rounded-lg font-medium transition-colors"
        >
          Explorar Artigos
          <IconArrowRight class="inline-block ml-2 rtl:-rotate-180" />
        </LinkButton>
        <button
          onclick="openNewsletterModal()"
          class="inline-flex items-center gap-2 px-6 py-3 border border-accent text-accent hover:bg-accent hover:text-background rounded-lg font-medium transition-colors"
        >
          <IconMail
            width={18}
            height={18}
            class="stroke-current"
          />
          Receber por email
        </button>
        <a
          target="_blank"
          href="/rss.xml"
          class="inline-flex items-center gap-2 px-4 py-2 text-sm text-foreground/70 hover:text-accent transition-colors"
          aria-label="rss feed"
          title="RSS Feed"
        >
          <IconRss
            width={18}
            height={18}
            class="stroke-current"
          />
          <span>RSS Feed</span>
        </a>
      </div>

      {
        // only display if at least one social link is enabled
        SOCIALS.length > 0 && (
          <div class="flex flex-col sm:flex-row sm:items-center gap-3">
            <span class="text-sm text-foreground/60 font-medium">Conecte-se:</span>
            <Socials />
          </div>
        )
      }
    </section>

    <Hr />

    {
      featuredPosts.length > 0 && (
        <>
          <section id="featured" class="pt-12 pb-8">
            <div class="flex items-center gap-3 mb-8">
              <h2 class="text-3xl font-bold text-accent">Artigos em Destaque</h2>
              <div class="h-px bg-border flex-1"></div>
            </div>
            <div class="mb-4 text-foreground/70">
              Os artigos mais recentes de destaque
            </div>
            <ul class="space-y-6">
              {featuredPosts.map(data => (
                <Card variant="h3" {...data} />
              ))}
            </ul>
          </section>
          {recentPosts.length > 0 && <Hr />}
        </>
      )
    }

    {
      recentPosts.length > 0 && (
        <section id="recent-posts" class="pt-12 pb-8">
          <div class="flex items-center gap-3 mb-8">
            <h2 class="text-3xl font-bold text-accent">Publicações Recentes</h2>
            <div class="h-px bg-border flex-1"></div>
          </div>
          <div class="mb-4 text-foreground/70">
            Últimas reflexões e descobertas do mundo da saúde
          </div>
          <ul class="space-y-6">
            {recentPosts.map(
              (data, index) =>
                index < SITE.postPerIndex && <Card variant="h3" {...data} />
            )}
          </ul>
        </section>
      )
    }

   

    <Hr />

    <section class="py-12 text-center bg-muted/30 rounded-lg">
      <h3 class="text-2xl font-semibold mb-4 text-accent">Explore Mais Conteúdo</h3>
      <p class="text-foreground/70 mb-6 max-w-lg mx-auto">
        Descobra todos os artigos sobre saúde pública, medicina e tecnologias em saúde
      </p>
      <div class="flex flex-wrap justify-center gap-4">
        <LinkButton 
          href="/posts/"
          class="bg-accent text-background hover:bg-accent/90 hover:text-background px-6 py-3 rounded-lg font-medium transition-colors"
        >
          Todas as Postagens
          <IconArrowRight class="inline-block ml-2 rtl:-rotate-180" />
        </LinkButton>
        <LinkButton 
          href="/tags/"
          class="border border-border hover:bg-muted px-6 py-3 rounded-lg font-medium transition-colors"
        >
          Explorar por Temas
        </LinkButton>
      </div>
    </section>
  </main>
  <Footer />
  
  <!-- Newsletter Modal -->
  <NewsletterModal />
</Layout>

<script>
  document.addEventListener("astro:page-load", () => {
    const indexLayout = (document.querySelector("#main-content") as HTMLElement)
      ?.dataset?.layout;
    if (indexLayout) {
      sessionStorage.setItem("backUrl", "/");
    }
  });
</script>
