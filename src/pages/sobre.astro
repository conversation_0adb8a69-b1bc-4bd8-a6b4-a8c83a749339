---
import Layout from "@/layouts/Layout.astro";
import Header from "@/components/Header.astro";
import Footer from "@/components/Footer.astro";
import Breadcrumb from "@/components/Breadcrumb.astro";
import { SITE } from "@/config";
import fotoAlex from "@/assets/images/foto-alex.webp";
---

<Layout title={`Sobre | ${SITE.title}`}>
  <Header />
  <Breadcrumb />
  <main id="main-content">
    <section id="about" class="app-prose mb-28 max-w-app">
      <h1 class="text-2xl tracking-wider sm:text-3xl mb-8">Sobre</h1>
      
      <div class="intro-text mb-8">
        <p class="text-base mb-3 leading-relaxed sm:text-lg sm:mb-4">
          Saúde com Alex é um projeto que começou com um pequeno projeto de universidade, quando a ideia cresceu mais do que eu esperava.
        </p>
        <p class="text-base leading-relaxed sm:text-lg">
          As ambições desse projeto são tampouco minimalistas, espero iniciar esse site e ir alimentando com notícias, pesquisas, métodos e opiniões que acho interessante ter algum lugar para compartilhar com o mundo.
        </p>
      </div>

      <h2 class="text-lg font-semibold mb-6 sm:text-xl sm:mb-8">Sobre o Autor</h2>
      
      <div class="author-section">
        <div class="author-photo">
          <img src={fotoAlex.src} alt="Alexandre Borges Filho" />
        </div>
        <div class="author-content">
          <p class="text-base mb-4 leading-relaxed">
            Alexandre Borges Filho é estudante de Medicina na Universidade de Fortaleza. Em 2025.1 ele se encontra no 9º Período, correspondente ao 1º Semestre do Internato, ou I1.
          </p>
          <p class="text-base leading-relaxed">
            Apaixonado por tecnologia e saúde pública, Alexandre combina seus conhecimentos médicos com ferramentas digitais para criar soluções que podem impactar positivamente o sistema de saúde brasileiro.
          </p>
        </div>
      </div>

      <h2 class="text-lg font-semibold mb-3 mt-8 sm:text-xl sm:mb-4 sm:mt-12">Sobre o site</h2>
      <p class="text-base leading-relaxed">
        O site em si é uma modificação do tema <a href="https://github.com/satnaing/astro-paper" class="text-accent hover:underline" target="_blank" rel="noopener noreferrer">AstroPaper</a>. É um projeto pensado para ser leve, altamente compatível com diversos dispositivos e acessível para leitores de texto e demais auxiliadores de navegação modernos.
      </p>
    </section>
  </main>
  <Footer />
</Layout>

<style>
  .intro-text {
    background: linear-gradient(135deg, rgba(var(--color-accent), 0.1), rgba(var(--color-accent), 0.05));
    border-left: 4px solid rgb(var(--color-accent));
    padding: 1rem;
    border-radius: 0.5rem;
  }

  @media (min-width: 640px) {
    .intro-text {
      padding: 1.5rem;
    }
  }

  .author-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
    background: linear-gradient(135deg, rgba(var(--color-accent), 0.05), transparent);
    border: 2px solid rgba(var(--color-accent), 0.1);
    border-radius: 1rem;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
  }

  .author-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, rgb(var(--color-accent)), rgba(var(--color-accent), 0.5));
    border-radius: 3px 3px 0 0;
  }

  .author-photo {
    position: relative;
    display: flex;
    justify-content: center;
  }

 

  .author-photo:hover::before {
    opacity: 1;
    transform: scale(1.02);
  }

  .author-photo img {
    width: 180px;
    height: 220px;
    object-fit: cover;
    border-radius: 12px;
    position: relative;
    z-index: 1;
    border: 3px solid rgb(var(--color-fill));
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .author-photo img:hover {
    transform: translateY(-3px);
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.2);
  }

  @keyframes shimmer {
    0%, 100% { 
      opacity: 0.3;
      transform: translateX(-100%);
    }
    50% { 
      opacity: 0.8;
      transform: translateX(100%);
    }
  }

  .author-content {
    text-align: center;
    max-width: 100%;
  }



  /* Desktop layout */
  @media (min-width: 768px) {
    .author-section {
      flex-direction: row;
      text-align: left;
      align-items: center;
      gap: 3rem;
    }

    .author-photo {
      flex-shrink: 0;
    }

    .author-photo img {
      width: 280px;
      height: 350px;
    }

    .author-content {
      text-align: left;
      flex: 1;
    }
  }

  /* Enhanced responsive design */
  @media (min-width: 1024px) {
    .author-photo img {
      width: 320px;
      height: 400px;
    }
  }

  /* Dark mode enhancements */
  html[data-theme="dark"] .author-section {
    background: linear-gradient(135deg, rgba(var(--color-accent), 0.08), transparent);
    border-color: rgba(var(--color-accent), 0.15);
  }

  html[data-theme="dark"] .intro-text {
    background: linear-gradient(135deg, rgba(var(--color-accent), 0.08), rgba(var(--color-accent), 0.03));
  }
</style> 