---
import Layout from "@/layouts/Layout.astro";
import Header from "@/components/Header.astro";
import Footer from "@/components/Footer.astro";
import LinkButton from "@/components/LinkButton.astro";
import { SITE } from "@/config";

const layoutProps = {
  title: `Cancelar Inscrição | ${SITE.title}`,
  description: "Cancelar inscrição da newsletter do Saúde com Alex",
};
---

<Layout {...layoutProps}>
  <Header />
  <main id="main-content" class="mx-auto w-full max-w-app px-4 py-12">
    <div class="mx-auto max-w-2xl text-center">
      
      <!-- Loading State -->
      <div id="loading-state" class="space-y-8">
        <div class="text-6xl mb-4">⏳</div>
        <div>
          <h1 class="text-3xl font-bold text-accent mb-4">
            Processando cancelamento...
          </h1>
          <p class="text-lg text-foreground/80 mb-6">
            Aguarde enquanto processamos sua solicitação.
          </p>
        </div>
        <div class="flex justify-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-accent"></div>
        </div>
      </div>

      <!-- Success State -->
      <div id="success-state" class="space-y-8 hidden">
        <div class="text-6xl mb-4">✅</div>
        <div>
          <h1 class="text-3xl font-bold text-accent mb-4">
            Inscrição Cancelada com Sucesso
          </h1>
          <p class="text-lg text-foreground/80 mb-6">
            Sua inscrição na newsletter <strong>Saúde com Alex</strong> foi cancelada.
          </p>
        </div>
        
        <div class="bg-green-50 border border-green-200 rounded-xl p-6 text-green-800">
          <div class="flex items-center justify-center mb-4">
            <span class="text-2xl">📧</span>
          </div>
          <h3 class="font-semibold mb-2">Cancelamento Confirmado</h3>
          <p class="text-sm">
            Você não receberá mais emails da nossa newsletter. 
            Lamentamos vê-lo partir, mas respeitamos sua decisão.
          </p>
        </div>
        
        <div class="space-y-4">
          <h3 class="text-xl font-semibold text-accent">Mesmo assim, você pode:</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
            <div class="bg-muted/30 rounded-lg p-4">
              <div class="text-2xl mb-2">📚</div>
              <h4 class="font-medium mb-1">Ler Artigos</h4>
              <p class="text-sm text-foreground/70">Continue explorando nossos conteúdos no site</p>
            </div>
            <div class="bg-muted/30 rounded-lg p-4">
              <div class="text-2xl mb-2">🔗</div>
              <h4 class="font-medium mb-1">Seguir nas Redes</h4>
              <p class="text-sm text-foreground/70">Acompanhe novidades nas redes sociais</p>
            </div>
            <div class="bg-muted/30 rounded-lg p-4">
              <div class="text-2xl mb-2">📡</div>
              <h4 class="font-medium mb-1">RSS Feed</h4>
              <p class="text-sm text-foreground/70">Use nosso feed RSS para receber atualizações</p>
            </div>
            <div class="bg-muted/30 rounded-lg p-4">
              <div class="text-2xl mb-2">↩️</div>
              <h4 class="font-medium mb-1">Voltar Quando Quiser</h4>
              <p class="text-sm text-foreground/70">Você pode se inscrever novamente a qualquer momento</p>
            </div>
          </div>
        </div>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <LinkButton 
            href="/posts/" 
            class="bg-accent text-background hover:bg-accent/90 hover:text-background px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Explorar Artigos
          </LinkButton>
          <LinkButton 
            href="/" 
            class="border border-border hover:bg-muted px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Voltar ao Início
          </LinkButton>
        </div>
      </div>

      <!-- Error State -->
      <div id="error-state" class="space-y-8 hidden">
        <div class="text-6xl mb-4" id="error-icon">❌</div>
        <div>
          <h1 class="text-3xl font-bold text-red-600 mb-4" id="error-title">
            Erro no Cancelamento
          </h1>
          <p class="text-lg text-foreground/80 mb-6" id="error-message">
            Houve um problema ao processar o cancelamento.
          </p>
        </div>
        
        <div class="bg-red-50 border border-red-200 rounded-xl p-6 text-red-800">
          <div class="flex items-center justify-center mb-4">
            <span class="text-2xl">⚠️</span>
          </div>
          <h3 class="font-semibold mb-2" id="error-help-title">
            Problema no Cancelamento
          </h3>
          <p class="text-sm" id="error-help-text">
            O link de cancelamento pode estar incorreto ou já ter sido usado.
          </p>
        </div>
        
        <div class="space-y-4">
          <h3 class="text-xl font-semibold text-accent">O que fazer:</h3>
          <div class="text-left space-y-3">
            <div class="flex items-start gap-3">
              <span class="text-accent font-bold">1.</span>
              <p>Verifique se o link está correto e completo</p>
            </div>
            <div class="flex items-start gap-3">
              <span class="text-accent font-bold">2.</span>
              <p>Tente acessar o link diretamente do email recebido</p>
            </div>
            <div class="flex items-start gap-3">
              <span class="text-accent font-bold">3.</span>
              <p>Entre em contato conosco através das redes sociais se o problema persistir</p>
            </div>
          </div>
        </div>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <LinkButton 
            href="/" 
            class="bg-accent text-background hover:bg-accent/90 hover:text-background px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Voltar ao Início
          </LinkButton>
          <LinkButton 
            href="/posts/" 
            class="border border-border hover:bg-muted px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Explorar Artigos
          </LinkButton>
        </div>
      </div>
      
      <div class="mt-12 pt-8 border-t border-border">
        <p class="text-sm text-foreground/60">
          Precisa de ajuda? Entre em contato através das redes sociais disponíveis no rodapé da página.
        </p>
      </div>
    </div>
  </main>
  <Footer />
</Layout>

<script>
  import { ConvexHttpClient } from 'convex/browser';

  document.addEventListener('astro:page-load', async () => {
    // Get token from URL query parameter (e.g., /unsubscribe?token=abc123)
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');

    if (!token) {
      window.location.href = '/404';
      return;
    }

    // Initialize Convex client
    const CONVEX_URL = import.meta.env.PUBLIC_CONVEX_URL || '';
    
    // Get DOM elements
    const loadingState = document.getElementById('loading-state');
    const successState = document.getElementById('success-state');
    const errorState = document.getElementById('error-state');
    const errorIcon = document.getElementById('error-icon');
    const errorTitle = document.getElementById('error-title');
    const errorMessage = document.getElementById('error-message');
    const errorHelpTitle = document.getElementById('error-help-title');
    const errorHelpText = document.getElementById('error-help-text');

    if (!loadingState || !successState || !errorState) return;

    const showSuccess = () => {
      loadingState.classList.add('hidden');
      successState.classList.remove('hidden');
      errorState.classList.add('hidden');
    };

    const showError = (isAlreadyUnsubscribed = false, message = '') => {
      loadingState.classList.add('hidden');
      successState.classList.add('hidden');
      errorState.classList.remove('hidden');

      if (isAlreadyUnsubscribed && errorIcon && errorTitle && errorMessage && errorHelpTitle && errorHelpText) {
        errorIcon.textContent = 'ℹ️';
        errorTitle.textContent = 'Já Cancelado';
        errorMessage.textContent = message || 'Esta inscrição já foi cancelada anteriormente.';
        errorHelpTitle.textContent = 'Informação';
        errorHelpText.textContent = 'Sua inscrição já estava inativa. Você não está recebendo nossos emails.';
      } else if (errorMessage) {
        errorMessage.textContent = message || 'Houve um problema ao processar o cancelamento.';
      }
    };

    try {
      if (!CONVEX_URL) {
        // Fallback simulation for when Convex is not configured
        await simulateUnsubscribe(token);
        return;
      }

      const convex = new ConvexHttpClient(CONVEX_URL);

      // First, get subscriber info
      // @ts-expect-error - Query reference will be available after convex dev
      const subscriberInfo = await convex.query('newsletter:getSubscriberByUnsubscribeToken', { token });
      
      if (!subscriberInfo) {
        showError(false, 'Token de cancelamento inválido.');
        return;
      }

      // Token is valid, now unsubscribe
      // @ts-expect-error - Mutation reference will be available after convex dev
      const result = await convex.mutation('newsletter:unsubscribeWithToken', { token });
      
      if (result.success) {
        showSuccess();
      } else {
        showError(result.code === 'ALREADY_UNSUBSCRIBED', result.message);
      }

    } catch {
      // Fallback to simulation if Convex fails
      await simulateUnsubscribe(token);
    }

    // Fallback simulation function
    async function simulateUnsubscribe(token: string) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate different scenarios based on token
      if (token === 'already-unsubscribed') {
        showError(true, 'Esta inscrição já foi cancelada anteriormente.');
        return;
      }
      
      if (token === 'invalid') {
        showError(false, 'Token de cancelamento inválido.');
        return;
      }
      
      // For demo purposes, most tokens are valid
      if (token.length >= 8) {
        showSuccess();
        return;
      }
      
      showError(false, 'Token de cancelamento inválido.');
    }
  });
</script> 