---
import { getCollection } from "astro:content";
import Main from "@/layouts/Main.astro";
import Layout from "@/layouts/Layout.astro";
import Tag from "@/components/Tag.astro";
import Header from "@/components/Header.astro";
import Footer from "@/components/Footer.astro";
import getUniqueTags from "@/utils/getUniqueTags";
import { SITE } from "@/config";

const posts = await getCollection("blog");

let tags = getUniqueTags(posts);
---

<Layout title={`Tags | ${SITE.title}`}>
  <Header />
  <Main pageTitle="Tags" pageDesc="Todas as tags usadas nas postagens.">
    <ul>
      {tags.map(({ tag, tagName }) => <Tag {tag} {tagName} size="lg" />)}
    </ul>
  </Main>
  <Footer />
</Layout>
