@plugin '@tailwindcss/typography';

@layer base {
  /* ===== Override default Tailwind Typography styles ===== */
  .app-prose {
    @apply prose;

    h1,
    h2,
    h3,
    h4,
    th {
      @apply mb-3 text-foreground;
    }

    h3 {
      @apply italic;
    }

    p,
    strong,
    ol,
    ul,
    figcaption,
    table,
    code {
      @apply text-foreground;
    }

    a {
      @apply break-words text-foreground decoration-dashed underline-offset-4 hover:text-accent focus-visible:no-underline;
    }

    ul {
      @apply overflow-x-clip;
    }

    li {
      @apply marker:text-accent;
    }

    hr {
      @apply border-border;
    }

    img {
      @apply mx-auto border border-border;
    }

    figcaption {
      @apply opacity-75;
    }

    table {
      th,
      td {
        @apply border border-border p-2;
      }

      th {
        @apply py-1.5;
      }

      code {
        @apply break-all sm:break-normal;
      }
    }

    code {
      @apply rounded bg-muted/75 p-1 break-words text-foreground before:content-none after:content-none;
    }

    .astro-code code {
      @apply bg-inherit p-0;
    }

    blockquote {
      @apply border-s-accent/80 break-words opacity-80;
    }

    details {
      @apply inline-block cursor-pointer text-foreground select-none [&_p]:hidden [&_ul]:!my-0;
    }

    summary {
      @apply focus-visible:no-underline focus-visible:outline-2 focus-visible:outline-offset-1 focus-visible:outline-accent focus-visible:outline-dashed;
    }

    pre {
      @apply focus-visible:border-transparent focus-visible:outline-2 focus-visible:outline-accent focus-visible:outline-dashed;
    }
  }

  /* ===== Code Blocks & Syntax Highlighting ===== */
  .astro-code {
    @apply border bg-(--shiki-light-bg) text-(--shiki-light) outline-border [&_span]:text-(--shiki-light);
  }

  html[data-theme="dark"] .astro-code {
    @apply bg-(--shiki-dark-bg) text-(--shiki-dark) [&_span]:text-(--shiki-dark);
  }

  /* Styles for Shiki transformers */
  /* https://shiki.style/packages/transformers */
  .astro-code {
    .line.diff.add {
      @apply relative *:bg-green-500/20 before:absolute before:-left-3 before:text-green-500 before:content-['+'];
    }
    .line.diff.remove {
      @apply relative *:bg-red-500/30 before:absolute before:-left-3 before:text-red-500 before:content-['-'];
    }
    .line.highlighted {
      @apply *:!bg-slate-400/20;
    }
    .highlighted-word {
      @apply rounded-sm border border-border px-0.5 py-px;
    }
  }
}
