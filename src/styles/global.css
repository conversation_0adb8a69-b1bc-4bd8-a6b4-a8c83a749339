@import "tailwindcss";
@import "./typography.css";

/* Peridot Font */
@font-face {
  font-family: 'Peridot';
  src: url('/assets/peridot.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@custom-variant dark (&:where([data-theme=dark], [data-theme=dark] *));

:root,
html[data-theme="light"] {
  --background: #fefefe;
  --foreground: #282728;
  --accent: #006cac;
  --muted: #e6e6e6;
  --border: #ece9e9;
}

html[data-theme="dark"] {
  --background: #212737;
  --foreground: #eaedf3;
  --accent: #ff6b01;
  --muted: #343f60bf;
  --border: #ab4b08;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-accent: var(--accent);
  --color-muted: var(--muted);
  --color-border: var(--border);
  
  --font-family-sans: 'Peridot', serif;
  --font-family-serif: 'Peridot', serif;
  --font-family-mono: 'Peridot', serif;
}

@layer base {
  * {
    @apply border-border outline-accent/75;
    scrollbar-width: auto;
    scrollbar-color: var(--color-muted) transparent;
  }
  html {
    @apply overflow-y-scroll scroll-smooth;
  }
  body {
    @apply flex min-h-svh flex-col bg-background font-sans text-foreground selection:bg-accent/75 selection:text-background;
  }
  a,
  button {
    @apply outline-offset-1 outline-accent focus-visible:no-underline focus-visible:outline-2 focus-visible:outline-dashed;
  }
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
  section,
  footer {
    @apply mx-auto max-w-app px-4;
  }
}

@utility max-w-app {
  @apply max-w-3xl;
}

.active-nav {
  @apply underline decoration-wavy decoration-2 underline-offset-4;
}

/* Source: https://piccalil.li/blog/a-more-modern-css-reset/ */
/* Anything that has been anchored to should have extra scroll margin */
:target {
  scroll-margin-block: 1rem;
}
